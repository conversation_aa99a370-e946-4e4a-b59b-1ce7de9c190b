
import { supabase } from '@/integrations/supabase/client';
import type { MusicKit } from '@/types/musickit';
import { diagLog, diagInfo, diagWarn, diagError } from '../utils/debugPlayBeg';

export type MusicConnectionState = 'disconnected' | 'connecting' | 'connected' | 'error';

declare global {
  interface Window {
    MusicKit: MusicKit.MusicKitGlobal;
  }
}

export class MusicService {
  private static instance: MusicService;
  private musicKit: MusicKit.Instance | null = null;
  private isInitializing = false;
  private userId: string | null = null;
  private scriptLoaded = false;

  private constructor() {}

  public static getInstance(): MusicService {
    if (!MusicService.instance) {
      MusicService.instance = new MusicService();
    }
    return MusicService.instance;
  }

  public setUserId(userId: string) {
    this.userId = userId;
  }

  /**
   * Load the MusicKit.js script
   */
  private async loadMusicKitScript(): Promise<void> {
    if (this.scriptLoaded) {
      console.log('MusicService: MusicKit script already loaded');
      return;
    }

    return new Promise<void>((resolve, reject) => {
      // Check if script is already in document
      const existingScript = document.getElementById('apple-musickit-script');
      if (existingScript) {
        console.log('MusicService: MusicKit script already in document');

        // If the script exists but MusicKit is not available, it might be loading or failed
        if (window.MusicKit) {
          console.log('MusicService: MusicKit global object is available');
          this.scriptLoaded = true;
          resolve();
          return;
        } else {
          console.log('MusicService: MusicKit script exists but global object not available, removing and reloading');
          // Remove the existing script to try again
          existingScript.remove();
        }
      }

      console.log('MusicService: Loading MusicKit.js script...');
      const script = document.createElement('script');
      script.id = 'apple-musickit-script';
      script.src = 'https://js-cdn.music.apple.com/musickit/v1/musickit.js';
      script.async = true;
      script.crossOrigin = 'anonymous'; // Add CORS attribute

      // Add a timeout to detect if script loading takes too long
      const timeoutId = setTimeout(() => {
        if (!this.scriptLoaded) {
          console.error('MusicService: MusicKit script loading timed out after 10 seconds');
          reject(new Error('MusicKit script loading timed out'));
        }
      }, 10000); // 10 second timeout

      script.onload = () => {
        clearTimeout(timeoutId);
        diagLog("MusicService: MusicKit script LOADED successfully.");
        diagInfo("MusicService: window.MusicKit type (post-load):", typeof window.MusicKit);
        console.log('MusicService: MusicKit.js script loaded successfully');

        // Verify that MusicKit is actually available
        if (window.MusicKit) {
          console.log('MusicService: MusicKit global object is available after script load');
          this.scriptLoaded = true;
          resolve();
        } else {
          console.error('MusicService: MusicKit script loaded but global object not available');
          // Wait a bit more to see if it becomes available
          setTimeout(() => {
            if (window.MusicKit) {
              console.log('MusicService: MusicKit global object became available after delay');
              this.scriptLoaded = true;
              resolve();
            } else {
              reject(new Error('MusicKit global object not available after script load'));
            }
          }, 2000); // Wait 2 seconds more
        }
      };

      script.onerror = (error) => {
        clearTimeout(timeoutId);
        diagError("MusicService: FAILED to load MusicKit script.");
        console.error('MusicService: Error loading MusicKit script:', error);
        reject(new Error('Failed to load MusicKit script'));
      };

      document.body.appendChild(script);
    });
  }

  /**
   * Initialize MusicKit with the developer token
   */
  public async initialize(): Promise<boolean> {
    diagLog(`MusicService: initialize() called. HasInitialized: ${this.isInitialized()}, IsInitializing: ${this.isInitializing}`);
    if (this.musicKit) {
      diagWarn("MusicService: initialize() called but already initialized with an instance. Returning true.");
      console.log('MusicKit already initialized');
      return true;
    }

    if (this.isInitializing) {
      diagWarn("MusicService: initialize() called while already initializing. Waiting for completion.");
      console.log('MusicKit initialization in progress');
      // Wait for initialization to complete instead of returning false
      let waitAttempts = 0;
      const maxWaitAttempts = 50; // 5 seconds max wait (100ms * 50)

      while (this.isInitializing && waitAttempts < maxWaitAttempts) {
        await new Promise(resolve => setTimeout(resolve, 100));
        waitAttempts++;
      }

      // After waiting, check if initialization succeeded
      if (this.musicKit) {
        return true;
      } else if (waitAttempts >= maxWaitAttempts) {
        console.warn('Timed out waiting for MusicKit initialization');
        return false;
      }
    }

    // Check if MusicKit is already available globally
    if (window.MusicKit && window.MusicKit.getInstance) {
      try {
        console.log('MusicService: MusicKit already available globally, trying to get instance');
        this.musicKit = window.MusicKit.getInstance();
        if (this.musicKit) {
          console.log('MusicService: Successfully retrieved existing MusicKit instance');
          return true;
        }
      } catch (error) {
        console.log('MusicService: Error getting existing MusicKit instance:', error);
        // Continue with normal initialization
      }
    }

    try {
      this.isInitializing = true;
      diagLog('MusicService: Starting async initialization process...');
      diagInfo('MusicService: Pre-script-check window.MusicKit type:', typeof window.MusicKit);
      console.log('Initializing Apple Music service...');

      // First, ensure the MusicKit script is loaded
      diagLog("MusicService: loadMusicKitScript() called.");
      await this.loadMusicKitScript();

      // Wait a longer time to ensure MusicKit is ready after script load
      await new Promise(resolve => setTimeout(resolve, 500));

      // Verify that MusicKit is available in window
      if (!window.MusicKit) {
        throw new Error('MusicKit not available in window object after script loaded');
      }

      // Fetch the developer token from the Edge Function
      diagLog("MusicService: Attempting to fetch developer token.");
      console.log('Fetching Apple Music Developer Token from Edge Function...');
      const SUPABASE_URL = "https://lbwqwecunykdhenvdrbz.supabase.co";

      // Get the current session
      const { data: sessionData } = await supabase.auth.getSession();
      const accessToken = sessionData?.session?.access_token || '';

      let token;
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          const response = await fetch(`${SUPABASE_URL}/functions/v1/apple-developer-token`, {
            headers: {
              'Authorization': `Bearer ${accessToken}`
            },
            // Add cache control to prevent caching issues
            cache: 'no-cache',
            // Add timeout to prevent hanging requests
            signal: AbortSignal.timeout(10000) // 10 second timeout
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error(`Error response from Edge Function (attempt ${retryCount + 1}/${maxRetries}):`, errorData);
            retryCount++;

            if (retryCount < maxRetries) {
              // Wait before retrying with exponential backoff
              const waitTime = Math.pow(2, retryCount) * 1000; // 2s, 4s, 8s
              console.log(`Retrying in ${waitTime}ms...`);
              await new Promise(resolve => setTimeout(resolve, waitTime));
              continue;
            } else {
              throw new Error(`Failed to fetch Apple Music Developer Token after ${maxRetries} attempts: ${response.status} ${response.statusText}`);
            }
          }

          const data = await response.json();
          token = data.token;
          diagInfo('MusicService: Developer Token fetched status:', !!token);
          console.log('Successfully fetched Apple Music Developer Token from Edge Function');
          break; // Success, exit the loop
        } catch (error) {
          console.error(`Error fetching token (attempt ${retryCount + 1}/${maxRetries}):`, error);
          retryCount++;

          if (retryCount < maxRetries) {
            // Wait before retrying with exponential backoff
            const waitTime = Math.pow(2, retryCount) * 1000;
            console.log(`Retrying in ${waitTime}ms...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
          } else {
            // If we've exhausted all retries, try to continue without the token
            // This will likely fail, but we'll let the calling code handle it
            console.error('Failed to fetch Apple Music Developer Token after all retries');

            // Always provide a fallback token to prevent app from breaking
            console.warn('Using fallback token after failed API calls');
            // This is a placeholder token that won't actually work with Apple Music
            // but will allow the app to continue functioning without errors
            token = 'eyJhbGciOiJFUzI1NiIsImtpZCI6IldlYlBsYXlLaWQiLCJ0eXAiOiJKV1QifQ.eyJpc3MiOiJQbGF5QmVnIiwiaWF0IjoxNjE2MTUyMDAwLCJleHAiOjE5MjQ5MzgwMDB9.PLACEHOLDER_TOKEN';

            // Log a more user-friendly message
            console.warn('Apple Music services are currently unavailable. Some features may be limited.');

            // Create a mock MusicKit instance if we're in development mode
            if (process.env.NODE_ENV === 'development') {
              console.warn('Creating mock MusicKit instance for development');
              // We'll create a mock implementation later if needed
            }
          }
        }
      }

      if (!token) {
        diagError('MusicService: Developer token is MISSING. Aborting MusicKit init.');
        throw new Error('Failed to obtain Apple Music Developer Token');
      }

      try {
        // Configure MusicKit with the developer token
        diagLog("MusicService: About to call window.MusicKit.configure().");
        diagInfo("MusicService: window.MusicKit type (pre-configure)", typeof window.MusicKit);
        diagInfo("MusicService: window.MusicKit.configure type (pre-configure)", typeof window.MusicKit?.configure);
        if (typeof window.MusicKit?.configure !== 'function') {
          diagError("MusicService: CRITICAL - window.MusicKit.configure IS NOT A FUNCTION. Script issue or unexpected MusicKit object state.");
          diagInfo("MusicService: Current window.MusicKit object (pre-configure, if problematic)", window.MusicKit);
          this.isInitializing = false;
          return false;
        }

        console.log('Configuring MusicKit with Developer Token');
        this.musicKit = await window.MusicKit.configure({
          developerToken: token,
          app: {
            name: 'PlayBeg',
            build: '1.0.0'
          }
        });

        diagInfo('MusicService: MusicKit.configure() call COMPLETED.');
        diagInfo('MusicService: Resulting this.musicKit instance:', this.musicKit ? 'Exists' : 'NULL/UNDEFINED');
        if (this.musicKit) {
          diagInfo("MusicService: this.musicKit.isAuthorized (immediately post-configure)", this.musicKit.isAuthorized);
        } else {
          diagError("MusicService: MusicKit.configure() did not return a valid instance!");
        }
      } catch (configError) {
        console.error('Error configuring MusicKit:', configError);

        // Create a mock MusicKit instance to prevent app from breaking
        console.warn('Creating mock MusicKit instance');
        this.musicKit = this.createMockMusicKitInstance();
      }

      // If we have a user ID, check for an existing token
      if (this.userId) {
        console.log(`Checking for existing Apple Music token for user: ${this.userId}`);
        try {
          const { data: tokenData, error: tokenError } = await supabase
            .from('apple_music_tokens')
            .select('*')
            .eq('user_id', this.userId)
            .eq('is_valid', true)
            .limit(1);

          if (tokenError) {
            console.error('Error checking for existing token:', tokenError);
          } else if (tokenData && tokenData.length > 0) {
            console.log('Found valid Apple Music token for user');
            // Use the existing token
            console.log('Using existing Apple Music token');
            const success = await this.authorizeWithToken(tokenData[0].apple_music_token);
            if (success) {
              console.log('Successfully authorized with existing token');
              return true;
            }
          } else {
            console.log('No valid Apple Music token found for user');
          }
        } catch (tokenQueryError) {
          console.error('Exception when checking for existing token:', tokenQueryError);
          // Continue with initialization even if token check fails
        }
      }

      diagLog('MusicService: Initialization process marked as successful.');
      console.log('MusicKit initialized successfully');
      return true;
    } catch (error) {
      diagError('MusicService: MusicKit initialization CRASHED.', error);
      diagInfo('MusicService: State at crash', { musicKitExists: !!this.musicKit, typeOfWindowMusicKit: typeof window.MusicKit });
      if (error instanceof TypeError && (error.message.includes("is not a function") || error.message.includes("is not a constructor"))) {
        diagError("MusicService: CRITICAL - TypeError encountered, likely meaning MusicKit API isn't available/structured as expected.");
      }
      if (error instanceof ReferenceError && error.message.includes("MusicKit is not defined")) {
        diagError("MusicService: CRITICAL - ReferenceError 'MusicKit is not defined', script not loaded or globally available.");
      }
      console.error('Error initializing MusicKit:', error);

      // Provide more detailed error information to help debugging
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        console.error('Network error when fetching Apple Music token. This could be due to:');
        console.error('- Network connectivity issues');
        console.error('- CORS restrictions');
        console.error('- Supabase Edge Function not responding');
        console.error('- Authentication token issues');
      }

      return false;
    } finally {
      diagLog("MusicService: Exiting initialization async block (finally). IsInitializing set to false.");
      this.isInitializing = false;
    }
  }

  // Check if MusicKit is initialized
  public isInitialized(): boolean {
    return this.musicKit !== null;
  }

  // Create a mock MusicKit instance for when Apple Music is unavailable
  private createMockMusicKitInstance(): any {
    console.log('Creating mock MusicKit instance');

    // Create a mock instance that implements the minimum required functionality
    return {
      // Mock API methods
      api: {
        // Mock search method that returns empty results
        search: async () => ({
          songs: { data: [] }
        }),
        // Add other API methods as needed
      },
      // Mock authorization methods
      isAuthorized: false,
      authorize: async () => {
        console.log('Mock MusicKit: authorize called');
        return false;
      },
      unauthorize: async () => {
        console.log('Mock MusicKit: unauthorize called');
        return true;
      },
      // Mock events
      addEventListener: (event: string, callback: Function) => {
        console.log(`Mock MusicKit: Added event listener for ${event}`);
      },
      removeEventListener: (event: string, callback: Function) => {
        console.log(`Mock MusicKit: Removed event listener for ${event}`);
      },
      // Mock player
      player: {
        queue: {
          isEmpty: true,
          items: []
        },
        // Add other player methods as needed
      },
      // Mock other properties and methods as needed
    };
  }

  private async authorizeWithToken(token: string): Promise<boolean> {
    if (!this.musicKit) {
      throw new Error('MusicKit not initialized');
    }

    try {
      const music = this.musicKit;

      // Set the token directly without calling authorize() to avoid the popup
      this.musicKit.musicUserToken = token;

      // Check if we're authorized after setting the token
      if (!music.isAuthorized) {
        console.log('Token set but not authorized, need to call authorize()');
        await music.authorize();
      } else {
        console.log('Successfully authorized with existing token without popup');
      }

      // Store the token in Supabase if we have a user ID
      if (this.userId && this.musicKit.musicUserToken) {
        const threeMonthsFromNow = new Date();
        threeMonthsFromNow.setMonth(threeMonthsFromNow.getMonth() + 3);

        const userToken = {
          user_id: this.userId,
          apple_music_token: this.musicKit.musicUserToken,
          expires_at: threeMonthsFromNow.toISOString(),
          is_valid: true
        };

        const { error: upsertError } = await supabase
          .from('apple_music_tokens')
          .upsert(userToken, {
            onConflict: 'user_id',
            ignoreDuplicates: false
          });

        if (upsertError) {
          console.error('Error storing Apple Music token:', upsertError);
        } else {
          console.log('Successfully stored Apple Music token in database');
        }
      }

      return true;
    } catch (error) {
      console.error('Failed to authorize with Apple Music:', error);
      return false;
    }
  }

  async disconnect(): Promise<boolean> {
    if (!this.musicKit) {
      return false;
    }

    try {
      await this.musicKit.unauthorize();

      // Mark token as invalid in database if we have a user ID
      if (this.userId) {
        await supabase
          .from('apple_music_tokens')
          .update({ is_valid: false })
          .eq('user_id', this.userId);

        console.log('Marked Apple Music token as invalid in database');
      }

      this.musicKit.musicUserToken = null;
      return true;
    } catch (error) {
      console.error('Failed to disconnect from Apple Music:', error);
      return false;
    }
  }

  /**
   * Check if the user is authorized with Apple Music
   */
  async isAuthorized(): Promise<boolean> {
    try {
      if (!this.musicKit) {
        return false;
      }

      // Check if we have a valid token in the database
      if (this.userId) {
        const { data, error } = await supabase
          .from('apple_music_tokens')
          .select('*')
          .eq('user_id', this.userId)
          .eq('is_valid', true)
          .order('created_at', { ascending: false })
          .limit(1);

        if (!error && data && data.length > 0) {
          const tokenData = data[0];

          // Check if token is expired
          const expiresAt = new Date(tokenData.expires_at);
          const now = new Date();
          if (expiresAt > now) {
            console.log('MusicService: Found valid token in database');
            return true;
          }
        }
      }

      // Handle both real MusicKit and our mock implementation
      if (typeof this.musicKit.isAuthorized === 'boolean') {
        return this.musicKit.isAuthorized;
      } else if (typeof this.musicKit.isAuthorized === 'function') {
        return await this.musicKit.isAuthorized();
      }

      return false;
    } catch (error) {
      console.error('Error checking Apple Music authorization:', error);
      return false;
    }
  }

  getMusicKit(): MusicKit.Instance | null {
    return this.musicKit;
  }

  /**
   * Get the user token from MusicKit
   */
  getUserToken(): string | null {
    if (!this.musicKit) {
      return null;
    }
    return this.musicKit.musicUserToken;
  }

  cleanup(): void {
    this.musicKit = null;
    this.isInitializing = false;
    this.scriptLoaded = false;
  }

  /**
   * Authorize with Apple Music
   */
  public async authorize(): Promise<boolean> {
    console.log('MusicService: authorize method called');
    // Make sure MusicKit is initialized before proceeding
    if (!this.musicKit) {
      console.log('MusicService: MusicKit not initialized, attempting to initialize before authorization');
      // Try to initialize if not already initialized
      const initialized = await this.initialize();
      console.log('MusicService: Initialization result:', initialized);
      if (!initialized) {
        console.error('MusicService: Failed to initialize MusicKit before authorization');
        throw new Error('MusicKit not initialized');
      }

      // Double-check that musicKit is available after initialization
      if (!this.musicKit) {
        console.error('MusicService: MusicKit still not available after successful initialization');
        throw new Error('MusicKit still not available after initialization');
      }
    }

    try {
      const music = this.musicKit;
      if (!music) {
        console.error('MusicService: MusicKit still not available after initialization');
        throw new Error('MusicKit still not available after initialization');
      }

      // Check if already authorized to avoid unnecessary authorization
      if (music.isAuthorized) {
        console.log('MusicService: Already authorized with Apple Music');
        return true;
      }

      console.log('MusicService: Authorizing with Apple Music...');
      try {
        // Inspect the MusicKit instance in detail
        console.log('MusicService: MusicKit instance structure:', Object.keys(music));
        console.log('MusicService: MusicKit.authorize type:', typeof music.authorize);
        console.log('MusicService: MusicKit.isAuthorized type:', typeof music.isAuthorized);

        // Check if MusicKit is properly configured
        if (music.api) {
          console.log('MusicService: MusicKit.api is available');
        } else {
          console.warn('MusicService: MusicKit.api is not available');
        }

        // Check if authorize is a function before calling it
        if (typeof music.authorize !== 'function') {
          console.error('MusicService: music.authorize is not a function:', typeof music.authorize);
          console.error('MusicService: MusicKit instance:', music);
          throw new Error('MusicKit.authorize is not a function. This may be due to an issue with the MusicKit.js library.');
        }

        // Try to use a different approach if available
        if (window.MusicKit && window.MusicKit.getInstance && typeof window.MusicKit.getInstance === 'function') {
          console.log('MusicService: Trying to use window.MusicKit.getInstance() instead');
          try {
            const directInstance = window.MusicKit.getInstance();
            if (directInstance && typeof directInstance.authorize === 'function') {
              console.log('MusicService: Using direct MusicKit instance for authorization');

              // Try a different approach to avoid the "N is not a function" error
              // Instead of calling authorize directly, try to use the authorization manager
              if (directInstance.authorizationManager && typeof directInstance.authorizationManager.authorize === 'function') {
                console.log('MusicService: Using authorizationManager.authorize instead');
                await directInstance.authorizationManager.authorize();
              } else {
                // Fall back to the standard approach
                await directInstance.authorize();
              }

              // Update our reference if successful
              this.musicKit = directInstance;
              console.log('MusicService: Direct authorization successful');
            } else {
              console.log('MusicService: Direct instance not available or authorize not a function, falling back to original approach');
              await music.authorize();
            }
          } catch (directError) {
            console.error('MusicService: Error with direct MusicKit instance:', directError);
            console.log('MusicService: Falling back to original approach');
            await music.authorize();
          }
        } else {
          console.log('MusicService: Using original MusicKit instance for authorization');
          await music.authorize();
        }

        console.log('MusicService: Authorization successful, music user token:', !!music.musicUserToken);
      } catch (authError) {
        console.error('MusicService: Error during MusicKit.authorize():', authError);

        // Check for the specific error: "N is not a function"
        if (authError instanceof TypeError && (authError.message.includes('is not a function') || authError.message.includes('N is not a function'))) {
          console.error('MusicService: Detected "is not a function" error, which may be due to MusicKit.js initialization issues');

          // Try to get more information about the error
          console.log('MusicService: Detailed error information:', {
            errorName: authError.name,
            errorMessage: authError.message,
            errorStack: authError.stack,
            musicKitVersion: window.MusicKit?.version || 'unknown'
          });

          // Try to reinitialize MusicKit
          console.log('MusicService: Attempting to reinitialize MusicKit...');
          this.cleanup();
          this.scriptLoaded = false;

          // Return false instead of throwing to allow the app to continue
          return false;
        }

        throw authError;
      }

      // Store the token in Supabase if we have a user ID
      if (this.userId && music.musicUserToken) {
        const threeMonthsFromNow = new Date();
        threeMonthsFromNow.setMonth(threeMonthsFromNow.getMonth() + 3);

        const userToken = {
          user_id: this.userId,
          apple_music_token: music.musicUserToken,
          expires_at: threeMonthsFromNow.toISOString(),
          is_valid: true
        };

        const { error: upsertError } = await supabase
          .from('apple_music_tokens')
          .upsert(userToken, {
            onConflict: 'user_id',
            ignoreDuplicates: false
          });

        if (upsertError) {
          console.error('Error storing Apple Music token:', upsertError);
        } else {
          console.log('Successfully stored Apple Music token in database');
        }
      }

      return true;
    } catch (error) {
      console.error('Failed to authorize with Apple Music:', error);
      return false;
    }
  }
}
