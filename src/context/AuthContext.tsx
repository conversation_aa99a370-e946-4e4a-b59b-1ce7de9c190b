
import React, { createContext, useContext, useEffect, useState, ReactNode, useRef } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Session, User } from "@supabase/supabase-js";
import { useToast } from "@/components/ui/use-toast";
import { useNavigate, useLocation } from "react-router-dom";
import { DJProfile } from "@/lib/types";
import { diagLog, diagInfo, diagWarn, diagError } from '../utils/debugPlayBeg';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isLoading: boolean; // Alias for loading to match ProtectedRoute
  needsOnboarding: boolean;
  initialAuthCheck: boolean;
  completeOnboarding: () => Promise<void>;
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signUp: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signOut: () => Promise<{ error: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [needsOnboarding, setNeedsOnboarding] = useState(false);
  const [initialAuthCheck, setInitialAuthCheck] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const location = useLocation();

  // Track AuthProvider re-renders with render count
  const renderCountRef = useRef(0);
  renderCountRef.current++;

  diagLog(`AuthProvider: RENDER #${renderCountRef.current}`, {
    user: !!user,
    userId: user?.id,
    loading,
    needsOnboarding,
    initialAuthCheck,
    pathname: location.pathname,
    locationKey: location.key,
    timestamp: performance.now()
  });

  useEffect(() => {
    diagLog("AuthContext: useEffect for onAuthStateChange - subscribing.");
    console.log('🔍 AUTH: Initializing AuthContext at', new Date().toISOString());

    // Create a single async function to handle initial auth setup
    const initializeAuth = async () => {
      try {
        diagLog("AuthContext: Starting initial auth check...");

        // Get initial session
        const { data: { session: currentSession } } = await supabase.auth.getSession();

        if (currentSession) {
          diagInfo("AuthContext: getSession() on mount/effect run", { userId: currentSession.user.id, hasActiveSession: true });
        } else {
          diagWarn("AuthContext: getSession() on mount/effect run - NO active session found initially.");
        }

        console.log('🔍 AUTH: Initial session check at', new Date().toISOString());
        console.log('🔍 AUTH: Initial session user:', currentSession?.user?.id || 'null');
        console.log('🔍 AUTH: Window focus during initial session:', document.hasFocus());

        // CRITICAL: Set all auth states atomically in a single batch
        diagLog("AuthContext: Attempting to set user, initialAuthCheck=true and loading=false ATOMICALLY.");
        setUser(currentSession?.user ?? null);

        // Check if user needs onboarding BEFORE setting final states
        if (currentSession?.user) {
          await checkOnboardingStatus(currentSession.user.id);
        }

        // Set final states atomically
        setInitialAuthCheck(true);
        setLoading(false);
        diagLog("AuthContext: Initial auth check COMPLETED - initialAuthCheck=true, loading=false");

      } catch (error) {
        diagError("AuthContext: Error during initial auth setup", error);
        // Even on error, mark initial auth check as complete
        diagLog("AuthContext: Error occurred - setting initialAuthCheck=true and loading=false");
        setInitialAuthCheck(true);
        setLoading(false);
      }
    };

    // Start the initialization
    initializeAuth();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      diagLog(`AuthContext: onAuthStateChange event fired: ${event}`);
      if (session) {
        diagInfo("AuthContext: Session received from onAuthStateChange", { userId: session.user.id, event });
      } else {
        diagWarn("AuthContext: No session from onAuthStateChange event. Event:", event);
      }

      console.log('🔍 AUTH: Auth state change event:', event, 'at', new Date().toISOString());
      console.log('🔍 AUTH: New session user:', session?.user?.id || 'null');
      console.log('🔍 AUTH: Window focus during auth change:', document.hasFocus());
      console.log('🔍 AUTH: Document visibility during auth change:', document.visibilityState);

      // Only update user state after initial auth check is complete
      // This prevents race conditions during browser refresh
      diagLog(`AuthContext: onAuthStateChange - About to update user state. Event: ${event}, Session exists: ${!!session}, Current initialAuthCheck: ${initialAuthCheck}`);

      if (initialAuthCheck) {
        // Only update user state if initial auth check is complete
        diagLog("AuthContext: onAuthStateChange - Updating user state (initialAuthCheck is complete)");
        setUser(session?.user ?? null);
        // Loading should already be false from initial auth check
      } else {
        diagLog("AuthContext: onAuthStateChange - SKIPPING user state update (initialAuthCheck not complete yet)");
      }

      // Check if user needs onboarding
      if (session?.user) {
        checkOnboardingStatus(session.user.id);

        // Update last login timestamp for SIGNED_IN events (not for TOKEN_REFRESHED)
        if (event === 'SIGNED_IN') {
          console.log('✅ AUTH: Updating last login timestamp (single call from onAuthStateChange)');
          try {
            await supabase.rpc('update_last_login_timestamp', {
              user_id: session.user.id
            });
          } catch (loginTrackingError) {
            console.warn('Failed to update last login timestamp:', loginTrackingError);
          }
        }
      }
    });

    return () => {
      diagLog("AuthContext: useEffect for onAuthStateChange - unsubscribing.");
      subscription.unsubscribe();
    };
  }, []);

  // Function to check if user needs onboarding
  const checkOnboardingStatus = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('dj_profiles')
        .select('completed_onboarding')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error checking onboarding status:', error);
        return;
      }

      // If completed_onboarding is false or null, user needs onboarding
      const needsOnboardingValue = !data?.completed_onboarding;
      diagLog(`AuthContext: checkOnboardingStatus - Setting needsOnboarding to: ${needsOnboardingValue} (completed_onboarding: ${data?.completed_onboarding})`);
      setNeedsOnboarding(needsOnboardingValue);
    } catch (error) {
      console.error('Error checking onboarding status:', error);
    }
  };

  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (data?.user) {
      // Don't update timestamp here - let onAuthStateChange handle it to avoid duplicates
      console.log('✅ AUTH: signIn() completed, no duplicate timestamp update');

      // Redirect to dashboard on successful login
      navigate('/dashboard');
    }

    return { data, error };
  };

  const signUp = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          // Add any metadata needed for the trigger function
          display_name: email.split('@')[0],
        }
      }
    });
    return { data, error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (!error) {
      // Redirect to home page on successful logout
      navigate('/');
    }
    return { error };
  };

  const completeOnboarding = async () => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('dj_profiles')
        .update({ completed_onboarding: true })
        .eq('id', user.id);

      if (error) {
        console.error('Error completing onboarding:', error);
        toast({
          title: 'Error',
          description: 'Failed to complete onboarding. Please try again.',
          variant: 'destructive'
        });
        return;
      }

      setNeedsOnboarding(false);
      toast({
        title: 'Onboarding Complete',
        description: 'Your profile has been set up successfully!'
      });

      // Redirect to dashboard
      navigate('/dashboard');
    } catch (error) {
      console.error('Error completing onboarding:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const value = {
    user,
    loading,
    isLoading: loading, // Alias for loading to match ProtectedRoute
    needsOnboarding,
    initialAuthCheck,
    completeOnboarding,
    signIn,
    signUp,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
