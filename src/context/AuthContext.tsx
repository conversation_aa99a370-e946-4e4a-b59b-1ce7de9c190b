
import React, { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Session, User } from "@supabase/supabase-js";
import { useToast } from "@/components/ui/use-toast";
import { useNavigate, useLocation } from "react-router-dom";
import { DJProfile } from "@/lib/types";
import { diagLog, diagInfo, diagWarn, diagError } from '../utils/debugPlayBeg';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isLoading: boolean; // Alias for loading to match ProtectedRoute
  needsOnboarding: boolean;
  initialAuthCheck: boolean;
  completeOnboarding: () => Promise<void>;
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signUp: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signOut: () => Promise<{ error: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [needsOnboarding, setNeedsOnboarding] = useState(false);
  const [initialAuthCheck, setInitialAuthCheck] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const location = useLocation();

  // Track AuthProvider re-renders
  diagLog(`AuthProvider: RENDER - user: ${!!user}, loading: ${loading}, needsOnboarding: ${needsOnboarding}, initialAuthCheck: ${initialAuthCheck}`);

  useEffect(() => {
    diagLog("AuthContext: useEffect for onAuthStateChange - subscribing.");
    console.log('🔍 AUTH: Initializing AuthContext at', new Date().toISOString());

    // Get initial session
    supabase.auth.getSession().then(({ data: { session: currentSession } }) => {
      if (currentSession) {
        diagInfo("AuthContext: getSession() on mount/effect run", { userId: currentSession.user.id, hasActiveSession: true });
      } else {
        diagWarn("AuthContext: getSession() on mount/effect run - NO active session found initially.");
      }

      console.log('🔍 AUTH: Initial session check at', new Date().toISOString());
      console.log('🔍 AUTH: Initial session user:', currentSession?.user?.id || 'null');
      console.log('🔍 AUTH: Window focus during initial session:', document.hasFocus());

      // Set user and loading state atomically to prevent race conditions
      setUser(currentSession?.user ?? null);
      setInitialAuthCheck(true);
      setLoading(false);

      // Check if user needs onboarding
      if (currentSession?.user) {
        checkOnboardingStatus(currentSession.user.id);
      }
    }).catch(error => {
      diagError("AuthContext: Error calling getSession()", error);
      // Even on error, mark initial auth check as complete
      setInitialAuthCheck(true);
      setLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      diagLog(`AuthContext: onAuthStateChange event fired: ${event}`);
      if (session) {
        diagInfo("AuthContext: Session received from onAuthStateChange", { userId: session.user.id, event });
      } else {
        diagWarn("AuthContext: No session from onAuthStateChange event. Event:", event);
      }

      console.log('🔍 AUTH: Auth state change event:', event, 'at', new Date().toISOString());
      console.log('🔍 AUTH: New session user:', session?.user?.id || 'null');
      console.log('🔍 AUTH: Window focus during auth change:', document.hasFocus());
      console.log('🔍 AUTH: Document visibility during auth change:', document.visibilityState);

      // Only update loading state after initial auth check is complete
      // This prevents race conditions during browser refresh
      diagLog(`AuthContext: onAuthStateChange - About to update user state. Event: ${event}, Session exists: ${!!session}, Current initialAuthCheck: ${initialAuthCheck}`);
      setUser(session?.user ?? null);
      if (initialAuthCheck) {
        diagLog("AuthContext: onAuthStateChange - Setting loading to false (initialAuthCheck is true)");
        setLoading(false);
      } else {
        diagLog("AuthContext: onAuthStateChange - NOT setting loading to false (initialAuthCheck is false)");
      }

      // Check if user needs onboarding
      if (session?.user) {
        checkOnboardingStatus(session.user.id);

        // Update last login timestamp for SIGNED_IN events (not for TOKEN_REFRESHED)
        if (event === 'SIGNED_IN') {
          console.log('✅ AUTH: Updating last login timestamp (single call from onAuthStateChange)');
          try {
            await supabase.rpc('update_last_login_timestamp', {
              user_id: session.user.id
            });
          } catch (loginTrackingError) {
            console.warn('Failed to update last login timestamp:', loginTrackingError);
          }
        }
      }
    });

    return () => {
      diagLog("AuthContext: useEffect for onAuthStateChange - unsubscribing.");
      subscription.unsubscribe();
    };
  }, []);

  // Function to check if user needs onboarding
  const checkOnboardingStatus = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('dj_profiles')
        .select('completed_onboarding')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error checking onboarding status:', error);
        return;
      }

      // If completed_onboarding is false or null, user needs onboarding
      const needsOnboardingValue = !data?.completed_onboarding;
      diagLog(`AuthContext: checkOnboardingStatus - Setting needsOnboarding to: ${needsOnboardingValue} (completed_onboarding: ${data?.completed_onboarding})`);
      setNeedsOnboarding(needsOnboardingValue);
    } catch (error) {
      console.error('Error checking onboarding status:', error);
    }
  };

  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (data?.user) {
      // Don't update timestamp here - let onAuthStateChange handle it to avoid duplicates
      console.log('✅ AUTH: signIn() completed, no duplicate timestamp update');

      // Redirect to dashboard on successful login
      navigate('/dashboard');
    }

    return { data, error };
  };

  const signUp = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          // Add any metadata needed for the trigger function
          display_name: email.split('@')[0],
        }
      }
    });
    return { data, error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (!error) {
      // Redirect to home page on successful logout
      navigate('/');
    }
    return { error };
  };

  const completeOnboarding = async () => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('dj_profiles')
        .update({ completed_onboarding: true })
        .eq('id', user.id);

      if (error) {
        console.error('Error completing onboarding:', error);
        toast({
          title: 'Error',
          description: 'Failed to complete onboarding. Please try again.',
          variant: 'destructive'
        });
        return;
      }

      setNeedsOnboarding(false);
      toast({
        title: 'Onboarding Complete',
        description: 'Your profile has been set up successfully!'
      });

      // Redirect to dashboard
      navigate('/dashboard');
    } catch (error) {
      console.error('Error completing onboarding:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const value = {
    user,
    loading,
    isLoading: loading, // Alias for loading to match ProtectedRoute
    needsOnboarding,
    initialAuthCheck,
    completeOnboarding,
    signIn,
    signUp,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
