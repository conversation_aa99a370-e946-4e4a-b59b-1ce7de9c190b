
import React, { createContext, useContext, useEffect, useState, ReactNode, useRef } from "react";
import { flushSync } from "react-dom";
import { supabase } from "@/integrations/supabase/client";
import { Session, User } from "@supabase/supabase-js";
import { useToast } from "@/components/ui/use-toast";
import { useNavigate, useLocation } from "react-router-dom";
import { DJProfile } from "@/lib/types";
import { diagLog, diagInfo, diagWarn, diagError } from '../utils/debugPlayBeg';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isLoading: boolean; // Alias for loading to match ProtectedRoute
  needsOnboarding: boolean;
  initialAuthCheck: boolean;
  completeOnboarding: () => Promise<void>;
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signUp: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signOut: () => Promise<{ error: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [needsOnboarding, setNeedsOnboarding] = useState(false);
  const [initialAuthCheck, setInitialAuthCheck] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const location = useLocation();

  // Track AuthProvider re-renders with render count
  const renderCountRef = useRef(0);
  renderCountRef.current++;

  diagLog(`AuthProvider: RENDER #${renderCountRef.current}`, {
    user: !!user,
    userId: user?.id,
    loading,
    needsOnboarding,
    initialAuthCheck,
    pathname: location.pathname,
    locationKey: location.key,
    timestamp: performance.now()
  });

  // Log mounting state
  diagLog("AuthContext: Mounting AuthProvider, initialAuthCheck state:", initialAuthCheck, "loading state:", loading);

  useEffect(() => {
    diagLog("AuthContext: useEffect for initial auth check - starting.");
    console.log('🔍 AUTH: Initializing AuthContext at', new Date().toISOString());

    // CRITICAL: Single async function to handle initial auth setup ONCE
    const performInitialAuthCheck = async () => {
      try {
        diagLog("AuthContext: Starting initial session check...");

        // Get initial session
        const { data: { session: currentSession } } = await supabase.auth.getSession();

        if (currentSession) {
          diagInfo("AuthContext: getSession() found active session", { userId: currentSession.user.id });
        } else {
          diagWarn("AuthContext: getSession() - NO active session found initially.");
        }

        console.log('🔍 AUTH: Initial session check at', new Date().toISOString());
        console.log('🔍 AUTH: Initial session user:', currentSession?.user?.id || 'null');
        console.log('🔍 AUTH: Window focus during initial session:', document.hasFocus());

        // Set user state first
        setUser(currentSession?.user ?? null);

        // Check onboarding status if user exists
        if (currentSession?.user) {
          await checkOnboardingStatus(currentSession.user.id);
        }

        // CRITICAL: Set initialAuthCheck and loading ATOMICALLY using flushSync
        diagLog("AuthContext: Initial session check COMPLETE. Setting initialAuthCheck=true AND loading=false ATOMICALLY.");
        flushSync(() => {
          setInitialAuthCheck(true);
          setLoading(false);
        });
        diagLog("AuthContext: Initial auth check COMPLETED - initialAuthCheck=true, loading=false");

      } catch (error) {
        diagError("AuthContext: Error during initial auth setup", error);
        // Even on error, complete the initialization to prevent infinite loading
        diagLog("AuthContext: Error occurred - setting initialAuthCheck=true and loading=false ATOMICALLY");
        setUser(null);
        flushSync(() => {
          setInitialAuthCheck(true);
          setLoading(false);
        });
      }
    };

    // Perform the initial check
    performInitialAuthCheck();

    // Listen for auth changes (AFTER initial check is complete)
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      diagLog(`AuthContext: onAuthStateChange event fired: ${event}`);
      if (session) {
        diagInfo("AuthContext: Session received from onAuthStateChange", { userId: session.user.id, event });
      } else {
        diagWarn("AuthContext: No session from onAuthStateChange event. Event:", event);
      }

      console.log('🔍 AUTH: Auth state change event:', event, 'at', new Date().toISOString());
      console.log('🔍 AUTH: New session user:', session?.user?.id || 'null');
      console.log('🔍 AUTH: Window focus during auth change:', document.hasFocus());
      console.log('🔍 AUTH: Document visibility during auth change:', document.visibilityState);

      // Skip INITIAL_SESSION events - these are handled by the initial auth check
      if (event === 'INITIAL_SESSION') {
        diagLog("AuthContext: onAuthStateChange - SKIPPING INITIAL_SESSION event (handled by initial auth check)");
        return;
      }

      // For all other events (SIGNED_IN, SIGNED_OUT, TOKEN_REFRESHED), update user state
      diagLog(`AuthContext: onAuthStateChange - Updating user state for event: ${event}, Session exists: ${!!session}`);
      setUser(session?.user ?? null);

      // For sign-in events, ensure loading is false (in case it was set to true for some reason)
      if (event === 'SIGNED_IN' && session?.user) {
        diagLog("AuthContext: onAuthStateChange - User signed in, ensuring loading is false");
        setLoading(false);
      }

      // Check if user needs onboarding
      if (session?.user) {
        checkOnboardingStatus(session.user.id);

        // Update last login timestamp for SIGNED_IN events (not for TOKEN_REFRESHED)
        if (event === 'SIGNED_IN') {
          console.log('✅ AUTH: Updating last login timestamp (single call from onAuthStateChange)');
          try {
            await supabase.rpc('update_last_login_timestamp', {
              user_id: session.user.id
            });
          } catch (loginTrackingError) {
            console.warn('Failed to update last login timestamp:', loginTrackingError);
          }
        }
      }
    });

    return () => {
      diagLog("AuthContext: useEffect for onAuthStateChange - unsubscribing.");
      subscription.unsubscribe();
    };
  }, []);

  // Function to check if user needs onboarding
  const checkOnboardingStatus = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('dj_profiles')
        .select('completed_onboarding')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error checking onboarding status:', error);
        return;
      }

      // If completed_onboarding is false or null, user needs onboarding
      const needsOnboardingValue = !data?.completed_onboarding;
      diagLog(`AuthContext: checkOnboardingStatus - Setting needsOnboarding to: ${needsOnboardingValue} (completed_onboarding: ${data?.completed_onboarding})`);
      setNeedsOnboarding(needsOnboardingValue);
    } catch (error) {
      console.error('Error checking onboarding status:', error);
    }
  };

  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (data?.user) {
      // Don't update timestamp here - let onAuthStateChange handle it to avoid duplicates
      console.log('✅ AUTH: signIn() completed, no duplicate timestamp update');

      // Redirect to dashboard on successful login
      navigate('/dashboard');
    }

    return { data, error };
  };

  const signUp = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          // Add any metadata needed for the trigger function
          display_name: email.split('@')[0],
        }
      }
    });
    return { data, error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (!error) {
      // Redirect to home page on successful logout
      navigate('/');
    }
    return { error };
  };

  const completeOnboarding = async () => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('dj_profiles')
        .update({ completed_onboarding: true })
        .eq('id', user.id);

      if (error) {
        console.error('Error completing onboarding:', error);
        toast({
          title: 'Error',
          description: 'Failed to complete onboarding. Please try again.',
          variant: 'destructive'
        });
        return;
      }

      setNeedsOnboarding(false);
      toast({
        title: 'Onboarding Complete',
        description: 'Your profile has been set up successfully!'
      });

      // Redirect to dashboard
      navigate('/dashboard');
    } catch (error) {
      console.error('Error completing onboarding:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const value = {
    user,
    loading,
    isLoading: loading, // Alias for loading to match ProtectedRoute
    needsOnboarding,
    initialAuthCheck,
    completeOnboarding,
    signIn,
    signUp,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
