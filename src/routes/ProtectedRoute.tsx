import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { motion } from "framer-motion";
import { diagLog, diagWarn } from "@/utils/debugPlayBeg";
import { useRef } from "react";

const ProtectedRoute = (props: any) => {
  const { user, isLoading, needsOnboarding, initialAuthCheck } = useAuth();
  const location = useLocation();
  const isOnboardingRoute = location.pathname === '/onboarding';

  // Track render count
  const renderCountRef = useRef(0);
  renderCountRef.current++;

  // Log ALL component props and state at every render
  diagLog(`ProtectedRoute: RENDER #${renderCountRef.current}`, {
    props,
    user: !!user,
    userId: user?.id,
    isLoading,
    initialAuthCheck,
    needsOnboarding,
    pathname: location.pathname,
    locationKey: location.key, // Important: location.key changes can cause remounts
    locationState: location.state,
    timestamp: performance.now()
  });

  // Track rapid state changes
  const prevStateRef = useRef({
    user: !!user,
    userId: user?.id,
    isLoading,
    initialAuthCheck,
    needsOnboarding,
    pathname: location.pathname,
    locationKey: location.key
  });

  const currentState = {
    user: !!user,
    userId: user?.id,
    isLoading,
    initialAuthCheck,
    needsOnboarding,
    pathname: location.pathname,
    locationKey: location.key
  };

  Object.keys(currentState).forEach(key => {
    if (prevStateRef.current[key as keyof typeof currentState] !== currentState[key as keyof typeof currentState]) {
      diagWarn(`ProtectedRoute: STATE CHANGE - ${key} changed from ${prevStateRef.current[key as keyof typeof currentState]} to ${currentState[key as keyof typeof currentState]}`);
    }
  });
  prevStateRef.current = currentState;

  // Log decision-making process before each return statement
  if (isLoading || !initialAuthCheck) {
    diagLog("ProtectedRoute: DECISION - Returning LoadingScreen", { isLoading, initialAuthCheck });
    return (
      <div className="flex h-screen items-center justify-center bg-black">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="flex flex-col items-center"
        >
          <motion.div
            animate={{
              rotate: 360,
              borderRadius: ["50% 50% 50% 50%", "30% 70% 70% 30%", "50% 50% 50% 50%"]
            }}
            transition={{
              repeat: Infinity,
              duration: 2,
              ease: "easeInOut"
            }}
            className="h-16 w-16 border-4 border-transparent border-t-purple-500 border-r-cyan-500"
          />
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="mt-4 text-gray-400"
          >
            Loading...
          </motion.p>
        </motion.div>
      </div>
    );
  }

  if (!user) {
    diagLog("ProtectedRoute: DECISION - Navigating to login", { user: !!user });
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (needsOnboarding && location.pathname !== '/onboarding') {
    diagLog("ProtectedRoute: DECISION - Navigating to onboarding", { needsOnboarding, pathname: location.pathname });
    return <Navigate to="/onboarding" state={{ from: location }} replace />;
  }

  if (!needsOnboarding && isOnboardingRoute) {
    diagLog("ProtectedRoute: DECISION - Navigating to dashboard from onboarding", { needsOnboarding, isOnboardingRoute });
    return <Navigate to="/dashboard" replace />;
  }

  diagLog("ProtectedRoute: DECISION - Rendering Outlet (Dashboard should stay mounted)");
  return <Outlet />;
};

export default ProtectedRoute;
