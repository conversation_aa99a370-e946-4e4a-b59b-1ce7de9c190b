import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { motion } from "framer-motion";
import { diagLog, diagWarn } from "@/utils/debugPlayBeg";

const ProtectedRoute = () => {
  const { user, isLoading, needsOnboarding, initialAuthCheck } = useAuth();
  const location = useLocation();
  const isOnboardingRoute = location.pathname === '/onboarding';

  // CRITICAL: Log every single render with detailed state
  diagLog(`ProtectedRoute: RENDER - user: ${!!user}, isLoading: ${isLoading}, initialAuthCheck: ${initialAuthCheck}, needsOnboarding: ${needsOnboarding}, path: ${location.pathname}`);

  // Log the decision-making process step by step
  if (isLoading) {
    diagLog("ProtectedRoute: DECISION - Showing loading screen (isLoading: true)");
  } else if (!user && initialAuthCheck) {
    diagLog("ProtectedRoute: DECISION - Redirecting to login (!user && initialAuthCheck)");
  } else if (!initialAuthCheck) {
    diagLog("ProtectedRoute: DECISION - Showing session restoration (!initialAuthCheck)");
  } else if (needsOnboarding && !isOnboardingRoute) {
    diagLog("ProtectedRoute: DECISION - Redirecting to onboarding (needsOnboarding && !isOnboardingRoute)");
  } else if (!needsOnboarding && isOnboardingRoute) {
    diagLog("ProtectedRoute: DECISION - Redirecting to dashboard (!needsOnboarding && isOnboardingRoute)");
  } else {
    diagLog("ProtectedRoute: DECISION - Rendering Outlet (Dashboard should mount/stay mounted)");
  }

  // Show loading state while auth is being checked
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-black">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="flex flex-col items-center"
        >
          <motion.div
            animate={{
              rotate: 360,
              borderRadius: ["50% 50% 50% 50%", "30% 70% 70% 30%", "50% 50% 50% 50%"]
            }}
            transition={{
              repeat: Infinity,
              duration: 2,
              ease: "easeInOut"
            }}
            className="h-16 w-16 border-4 border-transparent border-t-purple-500 border-r-cyan-500"
          />
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="mt-4 text-gray-400"
          >
            Loading...
          </motion.p>
        </motion.div>
      </div>
    );
  }

  // CRITICAL FIX: Only redirect to login if we've completed initial auth check
  // This prevents Dashboard unmounting during browser refresh authentication restoration
  if (!user && initialAuthCheck) {
    diagWarn("ProtectedRoute: Redirecting to login - user not authenticated after initial auth check");
    return <Navigate to="/login" replace />;
  }

  // If initial auth check is not complete, continue showing loading
  // This prevents race conditions during browser refresh
  if (!initialAuthCheck) {
    diagLog("ProtectedRoute: Showing session restoration loading - initial auth check not complete");
    return (
      <div className="flex h-screen items-center justify-center bg-black">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="flex flex-col items-center"
        >
          <motion.div
            animate={{
              rotate: 360,
              borderRadius: ["50% 50% 50% 50%", "30% 70% 70% 30%", "50% 50% 50% 50%"]
            }}
            transition={{
              repeat: Infinity,
              duration: 2,
              ease: "easeInOut"
            }}
            className="h-16 w-16 border-4 border-transparent border-t-purple-500 border-r-cyan-500"
          />
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="mt-4 text-gray-400"
          >
            Restoring session...
          </motion.p>
        </motion.div>
      </div>
    );
  }

  // If user needs onboarding and is not already on the onboarding page, redirect to onboarding
  if (needsOnboarding && !isOnboardingRoute) {
    return <Navigate to="/onboarding" replace />;
  }

  // If user has completed onboarding and tries to access the onboarding page, redirect to dashboard
  if (!needsOnboarding && isOnboardingRoute) {
    return <Navigate to="/dashboard" replace />;
  }

  return <Outlet />;
};

export default ProtectedRoute;
