
import { Outlet, useLocation } from "react-router-dom";
import Header from "./Header";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import PageViewTracker from "@/components/analytics/PageViewTracker";
import { diagLog } from "@/utils/debugPlayBeg";
import { useRef, useEffect } from "react";

interface MainLayoutProps {
  className?: string;
}

const MainLayout = ({ className }: MainLayoutProps) => {
  const location = useLocation();

  // Track render count and location changes
  const renderCountRef = useRef(0);
  const prevLocationRef = useRef({ pathname: location.pathname, key: location.key });
  const prevClassNameRef = useRef(className);
  renderCountRef.current++;

  diagLog(`MainLayout: RENDER #${renderCountRef.current}`, {
    pathname: location.pathname,
    locationKey: location.key,
    prevPathname: prevLocationRef.current.pathname,
    prevLocationKey: prevLocationRef.current.key,
    className,
    prevClassName: prevClassNameRef.current,
    timestamp: performance.now()
  });

  // Check for location.key changes that could cause remounts
  if (prevLocationRef.current.key !== location.key) {
    diagLog(`MainLayout: LOCATION KEY CHANGED from ${prevLocationRef.current.key} to ${location.key} (pathname: ${location.pathname})`);
  }
  if (prevLocationRef.current.pathname !== location.pathname) {
    diagLog(`MainLayout: PATHNAME CHANGED from ${prevLocationRef.current.pathname} to ${location.pathname}`);
  }
  if (prevClassNameRef.current !== className) {
    diagLog(`MainLayout: CLASSNAME CHANGED from ${prevClassNameRef.current} to ${className}`);
  }

  prevLocationRef.current = { pathname: location.pathname, key: location.key };
  prevClassNameRef.current = className;

  // Component mount/unmount tracking for MainLayout
  useEffect(() => {
    diagLog('MainLayout: Component MOUNTED');
    return () => {
      diagLog('MainLayout: Component UNMOUNTING - this could indicate router issues');
    };
  }, []);

  // Safely detect if the user prefers reduced motion (for accessibility)
  const prefersReducedMotion = typeof window !== 'undefined'
    ? window.matchMedia("(prefers-reduced-motion: reduce)").matches
    : false;

  // Safely detect if the user is on a mobile device
  const isMobile = typeof navigator !== 'undefined'
    ? /Mobi|Android/i.test(navigator.userAgent)
    : false;

  // Define the optimized background style
  const backgroundStyle = prefersReducedMotion || isMobile
    ? { background: "linear-gradient(90deg, rgba(128,0,255,0.15), rgba(0,255,255,0.1), rgba(128,0,255,0.15))" } // Static gradient for low-power devices
    : {
        background: "linear-gradient(90deg, rgba(128,0,255,0.15), rgba(0,255,255,0.1), rgba(128,0,255,0.15))",
        backgroundSize: "200% 200%",
        animation: "aurora 20s infinite alternate ease-in-out" // Smooth animation for desktops
      };

  return (
    <div className="min-h-screen bg-gradient-to-b from-black via-gray-900 to-black text-white relative mobile-vertical-scroll-only">
      {/* Aurora Animated Background */}
      <div className="fixed inset-0 pointer-events-none" style={backgroundStyle} />

      {/* Enhanced Glow Overlay */}
      <div className="fixed inset-0 bg-gradient-to-br from-purple-500/10 via-transparent to-cyan-500/10 pointer-events-none opacity-80" />

      {/* Grain texture overlay for depth */}
      <div className="fixed inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIiB4PSIwIiB5PSIwIj48ZmVUdXJidWxlbmNlIGJhc2VGcmVxdWVuY3k9Ii43NSIgc3RpdGNoVGlsZXM9InN0aXRjaCIgdHlwZT0iZnJhY3RhbE5vaXNlIi8+PGZlQ29sb3JNYXRyaXggdHlwZT0ic2F0dXJhdGUiIHZhbHVlcz0iMCIvPjwvZmlsdGVyPjxwYXRoIGQ9Ik0wIDBoMzAwdjMwMEgweiIgZmlsdGVyPSJ1cmwoI2EpIiBvcGFjaXR5PSIuMDUiLz48L3N2Zz4=')]
      pointer-events-none opacity-40" />

      <Header />

      {/* Track page views */}
      <PageViewTracker />

      <motion.main
        key="main-layout-stable" // CRITICAL: Stable key to prevent remounts
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className={cn("w-full relative z-10 mobile-vertical-scroll-only", className)}
      >
        <div className="w-full max-w-[1400px] mx-auto px-3 xs:px-4 sm:px-6 md:px-8 py-4 sm:py-6 md:py-8 mobile-vertical-scroll-only">
          <Outlet />
        </div>
      </motion.main>
    </div>
  );
};

export default MainLayout;
