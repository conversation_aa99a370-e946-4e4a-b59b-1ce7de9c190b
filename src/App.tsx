
import { RouterProvider } from 'react-router-dom';
import { ThemeProvider } from '@/context/ThemeContext';
import { MusicErrorBoundary } from '@/components/error/MusicErrorBoundary';
import { Toaster } from '@/components/ui/toaster';
import CookieConsentManager from '@/components/cookie/CookieConsentManager';
import router from './routes';
import { useEffect } from 'react';
import { initAnalytics } from '@/utils/simpleAnalytics';
import { HelmetProvider } from 'react-helmet-async';
import { diagLog, resetDebugTimer, diagInfo, diagWarn, diagError } from './utils/debugPlayBeg';

export default function App() {
  resetDebugTimer("Application Mount/Refresh");
  diagLog("App.tsx: Top-level execution (Mounting or Re-rendering after refresh).");

  function checkMusicKitScriptPresenceInitial() {
    diagLog("App.tsx: Checking for window.MusicKit script presence (initial)...");
    if (window.MusicKit && typeof window.MusicKit.configure === 'function') {
      diagInfo("App.tsx: window.MusicKit (initial check)", {
        defined: true,
        type: typeof window.MusicKit,
        version: window.MusicKit.version, // If MusicKit.version exists
        configurable: true
      });
    } else {
      diagWarn("App.tsx: window.MusicKit is UNDEFINED or not configurable at initial check. Script might not be loaded yet, failed, or is an unexpected version.");
      diagInfo("App.tsx: window.MusicKit object (initial check, if problematic)", window.MusicKit);
    }
  }
  checkMusicKitScriptPresenceInitial();

  // App mount detection removed - issue resolved

  // Initialize analytics based on cookie consent
  useEffect(() => {
    initAnalytics();
  }, []);

  return (
    <HelmetProvider>
      <ThemeProvider>
        <MusicErrorBoundary>
          <RouterProvider router={router} />
        </MusicErrorBoundary>
        <Toaster />
        <CookieConsentManager />
      </ThemeProvider>
    </HelmetProvider>
  );
}
