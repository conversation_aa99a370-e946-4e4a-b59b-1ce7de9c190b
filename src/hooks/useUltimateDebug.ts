/**
 * 🔧 ULTIMATE DEBUG HOOK
 * 
 * Automatically integrates components with the Ultimate Debug Tool
 * for comprehensive monitoring and root cause analysis.
 */

import { useEffect, useRef, useCallback } from 'react';
import { ultimateDebugTool } from '@/utils/ultimateDebugTool';

interface UseUltimateDebugOptions {
  componentName: string;
  trackProps?: boolean;
  trackState?: boolean;
  autoStart?: boolean;
}

export function useUltimateDebug(options: UseUltimateDebugOptions) {
  const { componentName, trackProps = false, trackState = false, autoStart = false } = options;
  const renderCountRef = useRef(0);
  const mountTimeRef = useRef(performance.now());
  const propsRef = useRef<any>(null);

  // Auto-start monitoring on first component mount if enabled
  useEffect(() => {
    if (autoStart && !ultimateDebugTool.isCurrentlyRecording()) {
      ultimateDebugTool.startMonitoring(`Auto-started by ${componentName}`);
    }
  }, [autoStart, componentName]);

  // Track component lifecycle
  useEffect(() => {
    ultimateDebugTool.trackComponentMount(componentName);
    
    return () => {
      ultimateDebugTool.trackComponentUnmount(componentName);
    };
  }, [componentName]);

  // Track renders
  renderCountRef.current++;
  const currentProps = trackProps ? propsRef.current : undefined;
  
  useEffect(() => {
    ultimateDebugTool.trackComponentRender(componentName, currentProps);
  });

  // Utility functions for manual tracking
  const trackEvent = useCallback((type: string, data: any, severity: 'INFO' | 'WARN' | 'ERROR' | 'CRITICAL' = 'INFO') => {
    ultimateDebugTool.logEvent(severity, 'COMPONENT', type, { component: componentName, ...data });
  }, [componentName]);

  const trackAuthChange = useCallback((event: string, fromState: any, toState: any) => {
    ultimateDebugTool.trackAuthStateChange(event, fromState, toState);
  }, []);

  const trackDataLoading = useCallback((isLoading: boolean, dataType: string, additionalData?: any) => {
    ultimateDebugTool.logEvent(
      isLoading ? 'INFO' : 'INFO',
      'DATA',
      isLoading ? 'LOADING_START' : 'LOADING_END',
      { component: componentName, dataType, isLoading, ...additionalData }
    );
  }, [componentName]);

  const trackError = useCallback((error: Error | string, context?: any) => {
    ultimateDebugTool.logEvent('ERROR', 'ERROR', 'COMPONENT_ERROR', {
      component: componentName,
      error: error.toString(),
      stack: error instanceof Error ? error.stack : undefined,
      context
    });
  }, [componentName]);

  // Update props reference for tracking
  const updateProps = useCallback((props: any) => {
    if (trackProps) {
      propsRef.current = props;
    }
  }, [trackProps]);

  return {
    // Tracking functions
    trackEvent,
    trackAuthChange,
    trackDataLoading,
    trackError,
    updateProps,
    
    // Component info
    renderCount: renderCountRef.current,
    mountTime: mountTimeRef.current,
    
    // Debug tool control
    startMonitoring: (testName?: string) => ultimateDebugTool.startMonitoring(testName),
    stopAndAnalyze: () => ultimateDebugTool.stopMonitoringAndAnalyze(),
    isRecording: ultimateDebugTool.isCurrentlyRecording(),
    getResults: () => ultimateDebugTool.getAnalysisResults()
  };
}

// Specialized hooks for common use cases
export function useAuthDebug() {
  const debug = useUltimateDebug({ componentName: 'AuthContext' });
  
  const trackAuthStateChange = useCallback((event: string, fromState: any, toState: any) => {
    debug.trackAuthChange(event, fromState, toState);
    
    // Additional auth-specific logging
    if (event === 'TOKEN_REFRESHED') {
      debug.trackEvent('TOKEN_REFRESH', {
        userIdChanged: fromState?.user?.id !== toState?.user?.id,
        loadingChanged: fromState?.loading !== toState?.loading
      }, 'WARN');
    }
  }, [debug]);

  return { ...debug, trackAuthStateChange };
}

export function useComponentDebug(componentName: string, props?: any) {
  const debug = useUltimateDebug({ 
    componentName, 
    trackProps: true,
    autoStart: componentName === 'Dashboard' // Auto-start for Dashboard
  });
  
  // Update props on every render
  useEffect(() => {
    debug.updateProps(props);
  }, [debug, props]);

  // Track prop changes
  const prevPropsRef = useRef(props);
  useEffect(() => {
    if (prevPropsRef.current !== props) {
      debug.trackEvent('PROPS_CHANGED', {
        prevProps: prevPropsRef.current,
        newProps: props
      });
      prevPropsRef.current = props;
    }
  }, [debug, props]);

  return debug;
}

export function useDataDebug(dataType: string) {
  const debug = useUltimateDebug({ componentName: `${dataType}Data` });
  
  const trackDataFetch = useCallback(async <T>(
    fetchFunction: () => Promise<T>,
    context?: any
  ): Promise<T> => {
    debug.trackDataLoading(true, dataType, context);
    const startTime = performance.now();
    
    try {
      const result = await fetchFunction();
      const duration = performance.now() - startTime;
      
      debug.trackDataLoading(false, dataType, { 
        success: true, 
        duration,
        resultSize: Array.isArray(result) ? result.length : 1,
        ...context 
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      debug.trackError(error as Error, { 
        dataType, 
        duration,
        fetchContext: context 
      });
      
      debug.trackDataLoading(false, dataType, { 
        success: false, 
        duration,
        error: (error as Error).message,
        ...context 
      });
      
      throw error;
    }
  }, [debug, dataType]);

  return { ...debug, trackDataFetch };
}

// Quick test functions for manual debugging
export const debugHelpers = {
  // Analyze recent activity (always works since background monitoring is active)
  analyzeNow: () => {
    console.log('🔧 Analyzing recent activity (last 30 seconds)...');
    return ultimateDebugTool.analyzeRecentActivity('Manual Analysis');
  },

  // Start a dashboard refresh test
  startDashboardRefreshTest: () => {
    console.log('🔧 Starting Dashboard Refresh Test...');
    console.log('🔧 Now REFRESH THE PAGE (F5) and wait 10 seconds!');
    ultimateDebugTool.startMonitoring('Dashboard Refresh Test');
  },

  // Start auth test
  startAuthTest: () => {
    console.log('🔧 Starting Auth State Test...');
    ultimateDebugTool.startMonitoring('Auth State Test');
  },

  // Get current results
  getResults: () => {
    const results = ultimateDebugTool.getAnalysisResults();
    if (results) {
      console.log('🔧 Current analysis results:', results);
    } else {
      console.log('🔧 No analysis results yet - try analyzeNow() to analyze recent activity');
    }
    return results;
  },

  // Quick refresh test - just refresh and analyze
  quickRefreshTest: () => {
    console.log('🔧 QUICK REFRESH TEST:');
    console.log('🔧 1. REFRESH THE PAGE NOW (F5)');
    console.log('🔧 2. Wait 3 seconds');
    console.log('🔧 3. Run debugHelpers.analyzeNow()');
  }
};

// Make helpers globally available for console testing
(window as any).debugHelpers = debugHelpers;
