import { useState, useCallback, useRef, useEffect } from "react";
import { supabase } from '@/integrations/supabase/client';
import { SessionWithSettings } from '@/types/session';
import { diagLog, diagWarn, diagError, diagInfo } from '../utils/debugPlayBeg';

interface Subscription {
  id: string;
  dj_id: string;
  plan_id: string | null;
  status: string;
  stripe_customer_id: string | null;
  stripe_subscription_id: string | null;
  current_period_start: string | null;
  current_period_end: string | null;
  cancel_at_period_end: boolean | null;
  created_at: string;
  updated_at: string;
}

export function useDashboardData(userId?: string) {
  diagLog(`useDashboardData: HOOK CALLED with userId: ${userId}`);

  const [sessions, setSessions] = useState<SessionWithSettings[]>([]);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const abortControllerRef = useRef<AbortController>();
  const subscriptionsRef = useRef<any[]>([]);

  // Track sessions state changes
  const prevSessionsLengthRef = useRef(sessions.length);
  if (prevSessionsLengthRef.current !== sessions.length) {
    diagLog(`useDashboardData: sessions state CHANGED from length ${prevSessionsLengthRef.current} to ${sessions.length}`);
    prevSessionsLengthRef.current = sessions.length;
  }

  // Only log once when userId changes
  const hasLoggedRef = useRef<string | undefined>();
  if (hasLoggedRef.current !== userId && process.env.NODE_ENV === 'development') {
    diagLog(`useDashboardData: Hook initialized with NEW userId: ${userId} (previous: ${hasLoggedRef.current})`);
    console.log('✅ DASHBOARD_DATA: Hook initialized with userId:', userId);
    hasLoggedRef.current = userId;
  }

  const fetchSessionsOnly = useCallback(async () => {
    if (!userId) {
      diagWarn("fetchSessionsOnly: No userId provided");
      return;
    }

    diagLog(`fetchSessionsOnly: STARTING for userId: ${userId}`);

    // Cancel any in-progress fetches
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this fetch
    abortControllerRef.current = new AbortController();

    diagLog("fetchSessionsOnly: Setting loading to true");
    setLoading(true);
    setError(null);

    try {
      diagLog("fetchSessionsOnly: Attempting to fetch sessions...");
      const sessionsResult = await supabase
        .from('sessions')
        .select('*')
        .eq('dj_id', userId)
        .order('created_at', { ascending: false });

      // Handle any errors
      if (sessionsResult.error) {
        diagError("fetchSessionsOnly: Error fetching sessions:", sessionsResult.error);
        throw sessionsResult.error;
      } else {
        diagInfo("fetchSessionsOnly: Successfully fetched sessions, count:", sessionsResult.data?.length);
      }

      // Update sessions state only
      diagLog("fetchSessionsOnly: About to set sessions state with count:", sessionsResult.data?.length || 0);
      setSessions(sessionsResult.data || []);
      diagLog("fetchSessionsOnly: Sessions state updated");
    } catch (err) {
      diagError("fetchSessionsOnly: CRITICAL ERROR during fetch:", err);
      console.error('Failed to fetch sessions:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch sessions'));
    } finally {
      diagLog("fetchSessionsOnly: Setting loading to false");
      setLoading(false);
      abortControllerRef.current = undefined;
      diagLog("fetchSessionsOnly: FINISHED");
    }
  }, [userId]);

  const fetchAllData = useCallback(async (): Promise<void> => {
    if (!userId) {
      diagWarn("useDashboardData (Fetch): Conditions not met for fetching data (e.g., no userId).");
      return Promise.resolve();
    }

    diagLog(`fetchAllData: STARTING for userId: ${userId}`);
    console.log('✅ DASHBOARD_DATA: fetchAllData called for userId:', userId);

    // Cancel any in-progress fetches
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this fetch
    abortControllerRef.current = new AbortController();

    diagLog("fetchAllData: About to set loading to true");
    setLoading(true);
    setError(null);
    diagLog("fetchAllData: Loading state set to true, error cleared");

    try {
      diagLog("fetchAllData: Attempting to fetch sessions and subscriptions in parallel...");

      // Fetch sessions and subscriptions in parallel
      const [sessionsResult, subscriptionsResult] = await Promise.all([
        supabase
          .from('sessions')
          .select('*')
          .eq('dj_id', userId)
          .order('created_at', { ascending: false }),
        supabase
          .from('dj_subscriptions')
          .select('*')
          .eq('dj_id', userId)
      ]);

      diagLog("fetchAllData: Parallel fetch completed, checking results...");

      // Handle any errors
      if (sessionsResult.error) {
        diagError("fetchAllData: Error fetching sessions:", sessionsResult.error);
        throw sessionsResult.error;
      } else {
        diagInfo("fetchAllData: Successfully fetched sessions, count:", sessionsResult.data?.length);
      }

      if (subscriptionsResult.error) {
        diagError("fetchAllData: Error fetching subscriptions:", subscriptionsResult.error);
        throw subscriptionsResult.error;
      } else {
        diagInfo("fetchAllData: Successfully fetched subscriptions, count:", subscriptionsResult.data?.length);
      }

      // Update state with new data
      diagLog("fetchAllData: About to set sessions state with count:", sessionsResult.data?.length || 0);
      setSessions(sessionsResult.data || []);
      diagLog("fetchAllData: Sessions state hopefully updated");

      diagLog("fetchAllData: About to set subscriptions state with count:", subscriptionsResult.data?.length || 0);
      setSubscriptions(subscriptionsResult.data || []);
      diagLog("fetchAllData: Subscriptions state hopefully updated");

    } catch (err) {
      diagError("fetchAllData: CRITICAL ERROR during fetch:", err);
      console.error('Failed to fetch dashboard data:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch data'));
    } finally {
      diagLog("fetchAllData: Setting loading to false");
      setLoading(false);
      abortControllerRef.current = undefined;
      diagLog("fetchAllData: FINISHED");
    }
  }, [userId]);

  // Track previous userId to detect changes
  const prevUserIdRef = useRef(userId);
  const currentUserIdRef = useRef<string | undefined>(undefined);
  const hasDataForUserRef = useRef(false);

  // Enhanced useEffect dependency tracking
  const effectDependencies = [userId]; // List ALL actual dependencies here
  const prevDepsRef = useRef(effectDependencies);

  // Fetch data and set up subscriptions when userId changes
  useEffect(() => {
    const currentDeps = [userId]; // Must match the dependency array exactly

    diagLog("useDashboardData: useEffect triggered", {
      currentUserId: userId,
      previousUserId: prevUserIdRef.current,
      hasDataForUser: hasDataForUserRef.current,
      currentUserIdRef: currentUserIdRef.current,
      loading,
      timestamp: performance.now()
    });

    // Check each dependency for changes
    let changedDependencies = [];
    for (let i = 0; i < currentDeps.length; i++) {
      if (currentDeps[i] !== prevDepsRef.current[i]) {
        changedDependencies.push({
          index: i,
          name: i === 0 ? 'userId' : `dependency_${i}`, // Name each dependency
          previous: prevDepsRef.current[i],
          current: currentDeps[i]
        });
      }
    }

    if (changedDependencies.length > 0) {
      diagLog("useDashboardData: Dependencies that changed:", changedDependencies);
    } else {
      diagWarn("useDashboardData: useEffect triggered but no dependencies changed - possible React issue or missing dependency");
    }

    prevDepsRef.current = [...currentDeps];

    if (prevUserIdRef.current !== userId) {
      diagLog("useDashboardData (Fetch) useEffect: userId HAS CHANGED.");
      hasDataForUserRef.current = false; // Reset data flag when userId changes
    } else {
      diagLog("useDashboardData (Fetch) useEffect: userId has NOT changed - investigating why useEffect triggered.");
    }
    prevUserIdRef.current = userId;

    if (!userId) {
      diagWarn("useDashboardData (Fetch): Conditions not met for fetching data (e.g., no userId).");
      return;
    }

    // Prevent unnecessary fetches if we already have data for this user and not currently loading
    if (hasDataForUserRef.current && currentUserIdRef.current === userId && !loading) {
      diagLog("useDashboardData (Fetch): SKIPPING fetchAllData - already have data for this userId and not loading");
      return;
    }

    // Prevent multiple simultaneous fetches
    if (loading && currentUserIdRef.current === userId) {
      diagLog("useDashboardData (Fetch): SKIPPING fetchAllData - already loading for this userId");
      return;
    }

    console.log('✅ DASHBOARD_DATA: Setting up data fetch and subscriptions for userId:', userId);
    currentUserIdRef.current = userId;

    // Fetch initial data
    fetchAllData().then(() => {
      hasDataForUserRef.current = true;
      diagLog("useDashboardData (Fetch): fetchAllData completed, marking hasDataForUserRef as true");
    }).catch((error) => {
      diagError("useDashboardData (Fetch): fetchAllData failed:", error);
    });

    // Set up realtime subscriptions
    const channelId = `dashboard-session-changes-${userId}`;
    diagLog(`useDashboardData (Realtime): Conditions met. Attempting to set up Supabase channel: ${channelId}`);
    const sessionChangesChannel = supabase
      .channel(channelId)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'sessions',
        filter: `dj_id=eq.${userId}`
      }, (_payload) => {
        console.log('✅ Session change detected, refreshing data');
        fetchAllData();
      })
      .subscribe();

    const subscriptionChannelId = `dashboard-subscription-changes-${userId}`;
    const subscriptionChangesChannel = supabase
      .channel(subscriptionChannelId)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'dj_subscriptions',
        filter: `dj_id=eq.${userId}`
      }, (_payload) => {
        console.log('✅ Subscription change detected, refreshing data');
        fetchAllData();
      })
      .subscribe();

    // Store subscriptions in ref for cleanup
    subscriptionsRef.current = [sessionChangesChannel, subscriptionChangesChannel];

    return () => {
      diagLog(`useDashboardData (Realtime): Cleaning up channel: ${channelId}`);
      console.log('✅ DASHBOARD_DATA: Cleaning up subscriptions for userId:', userId);
      subscriptionsRef.current.forEach(channel => {
        supabase.removeChannel(channel);
      });
      subscriptionsRef.current = [];
    };
  }, [userId]); // FIXED: Removed fetchAllData dependency to prevent loops

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    sessions,
    subscriptions,
    loading,
    error,
    fetchAllData,
    fetchSessionsOnly,
  };
}
