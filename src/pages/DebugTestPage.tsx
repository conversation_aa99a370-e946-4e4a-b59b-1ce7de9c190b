/**
 * 🔧 DEBUG TEST PAGE
 * 
 * Automated testing interface for identifying root causes of PlayBeg issues.
 * Provides one-click tests and real-time analysis.
 */

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ultimateDebugTool } from '@/utils/ultimateDebugTool';
import { debugHelpers } from '@/hooks/useUltimateDebug';
import { AlertCircle, CheckCircle, Clock, Download, Play, Square, RefreshCw } from 'lucide-react';

interface TestResult {
  testName: string;
  status: 'running' | 'completed' | 'failed';
  rootCause: string;
  confidence: number;
  evidence: string[];
  recommendations: string[];
  timestamp: string;
}

export default function DebugTestPage() {
  const [currentTest, setCurrentTest] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // Monitor debug tool state
  useEffect(() => {
    const interval = setInterval(() => {
      setIsMonitoring(ultimateDebugTool.isCurrentlyRecording());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Countdown timer for tests
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (countdown > 0) {
      interval = setInterval(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [countdown]);

  const runTest = async (testName: string, duration: number = 10) => {
    setCurrentTest(testName);
    setCountdown(duration);
    
    // Start monitoring
    ultimateDebugTool.startMonitoring(testName);
    
    // Wait for test duration
    setTimeout(() => {
      const results = ultimateDebugTool.stopMonitoringAndAnalyze();
      
      if (results) {
        const testResult: TestResult = {
          testName,
          status: 'completed',
          rootCause: results.rootCause,
          confidence: results.confidence,
          evidence: results.evidence,
          recommendations: results.recommendations,
          timestamp: new Date().toISOString()
        };
        
        setTestResults(prev => [testResult, ...prev]);
      }
      
      setCurrentTest(null);
      setCountdown(0);
    }, duration * 1000);
  };

  const stopCurrentTest = () => {
    if (currentTest) {
      const results = ultimateDebugTool.stopMonitoringAndAnalyze();
      
      if (results) {
        const testResult: TestResult = {
          testName: currentTest,
          status: 'completed',
          rootCause: results.rootCause,
          confidence: results.confidence,
          evidence: results.evidence,
          recommendations: results.recommendations,
          timestamp: new Date().toISOString()
        };
        
        setTestResults(prev => [testResult, ...prev]);
      }
      
      setCurrentTest(null);
      setCountdown(0);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-500';
    if (confidence >= 0.6) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">🔧 PlayBeg Debug Test Center</h1>
        <p className="text-muted-foreground">
          Automated root cause analysis for "Loading Sessions..." and connection issues.
        </p>
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-blue-600" />
            <span className="font-medium text-blue-800">Quick Start Instructions</span>
          </div>
          <ol className="text-sm text-blue-700 mt-2 list-decimal list-inside space-y-1">
            <li><strong>Refresh this page</strong> (F5 or Ctrl+R)</li>
            <li><strong>Wait 3 seconds</strong></li>
            <li><strong>Click "Analyze Recent Activity"</strong> below</li>
            <li><strong>See the root cause</strong> of your loading issues!</li>
          </ol>
        </div>

        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="font-medium text-green-800">Background Monitoring Active</span>
          </div>
          <p className="text-sm text-green-700 mt-1">
            Debug tool is continuously recording events. You can analyze recent activity at any time.
          </p>
        </div>
      </div>

      {/* Current Test Status */}
      {currentTest && (
        <Card className="mb-6 border-blue-500">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-blue-500" />
              Running Test: {currentTest}
            </CardTitle>
            <CardDescription>
              {countdown > 0 ? `${countdown} seconds remaining` : 'Analyzing results...'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="flex-1 bg-blue-100 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-1000"
                  style={{ width: `${((10 - countdown) / 10) * 100}%` }}
                />
              </div>
              <Button onClick={stopCurrentTest} variant="outline" size="sm">
                <Square className="w-4 h-4 mr-2" />
                Stop Test
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Analysis Button */}
      <Card className="mb-6 border-blue-500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="w-5 h-5 text-blue-500" />
            Quick Analysis
          </CardTitle>
          <CardDescription>
            Analyze the last 30 seconds of activity (works immediately)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            onClick={() => {
              const results = ultimateDebugTool.analyzeRecentActivity('Quick Analysis');
              if (results) {
                const testResult: TestResult = {
                  testName: 'Quick Analysis',
                  status: 'completed',
                  rootCause: results.rootCause,
                  confidence: results.confidence,
                  evidence: results.evidence,
                  recommendations: results.recommendations,
                  timestamp: new Date().toISOString()
                };
                setTestResults(prev => [testResult, ...prev]);
              }
            }}
            className="w-full"
            size="lg"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Analyze Recent Activity
          </Button>
          <p className="text-xs text-muted-foreground mt-2">
            <strong>Try this first!</strong> Refresh the page, then click this button to see what happened.
          </p>
        </CardContent>
      </Card>

      {/* Test Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">🔄 Dashboard Refresh Test</CardTitle>
            <CardDescription>
              Tests component stability during page refresh
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => runTest('Dashboard Refresh Test', 10)}
              disabled={!!currentTest}
              className="w-full"
            >
              <Play className="w-4 h-4 mr-2" />
              Start Test (10s)
            </Button>
            <p className="text-xs text-muted-foreground mt-2">
              Click this, then refresh the page to test stability
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">🔐 Auth State Test</CardTitle>
            <CardDescription>
              Tests authentication state transitions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => runTest('Auth State Test', 15)}
              disabled={!!currentTest}
              className="w-full"
            >
              <Play className="w-4 h-4 mr-2" />
              Start Test (15s)
            </Button>
            <p className="text-xs text-muted-foreground mt-2">
              Tests TOKEN_REFRESHED events and auth stability
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">🌐 WebSocket Test</CardTitle>
            <CardDescription>
              Tests WebSocket connection stability
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => runTest('WebSocket Connection Test', 12)}
              disabled={!!currentTest}
              className="w-full"
            >
              <Play className="w-4 h-4 mr-2" />
              Start Test (12s)
            </Button>
            <p className="text-xs text-muted-foreground mt-2">
              Tests Supabase Realtime connection issues
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">📊 Data Loading Test</CardTitle>
            <CardDescription>
              Tests session data loading persistence
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => runTest('Data Loading Test', 8)}
              disabled={!!currentTest}
              className="w-full"
            >
              <Play className="w-4 h-4 mr-2" />
              Start Test (8s)
            </Button>
            <p className="text-xs text-muted-foreground mt-2">
              Tests "Loading Sessions..." persistence issues
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">🔄 Component Lifecycle Test</CardTitle>
            <CardDescription>
              Tests component mount/unmount cycles
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => runTest('Component Lifecycle Test', 10)}
              disabled={!!currentTest}
              className="w-full"
            >
              <Play className="w-4 h-4 mr-2" />
              Start Test (10s)
            </Button>
            <p className="text-xs text-muted-foreground mt-2">
              Tests ProtectedRoute and Dashboard stability
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">🎯 Custom Test</CardTitle>
            <CardDescription>
              Manual test with custom duration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => {
                const duration = parseInt(prompt('Test duration in seconds:') || '10');
                const name = prompt('Test name:') || 'Custom Test';
                runTest(name, duration);
              }}
              disabled={!!currentTest}
              className="w-full"
            >
              <Play className="w-4 h-4 mr-2" />
              Custom Test
            </Button>
            <p className="text-xs text-muted-foreground mt-2">
              Define your own test parameters
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Test Results
            <Badge variant="outline">
              {testResults.length} test{testResults.length !== 1 ? 's' : ''} completed
            </Badge>
          </CardTitle>
          <CardDescription>
            Automated analysis results with root cause identification
          </CardDescription>
        </CardHeader>
        <CardContent>
          {testResults.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">
              No test results yet. Run a test to see analysis.
            </p>
          ) : (
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result.status)}
                      <h3 className="font-semibold">{result.testName}</h3>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge 
                        className={`text-white ${getConfidenceColor(result.confidence)}`}
                      >
                        {(result.confidence * 100).toFixed(0)}% confidence
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {new Date(result.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="mb-3">
                    <Badge variant="outline" className="text-sm">
                      Root Cause: {result.rootCause}
                    </Badge>
                  </div>

                  {result.evidence.length > 0 && (
                    <div className="mb-3">
                      <h4 className="text-sm font-medium mb-1">Evidence:</h4>
                      <ul className="text-sm text-muted-foreground list-disc list-inside">
                        {result.evidence.map((evidence, i) => (
                          <li key={i}>{evidence}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {result.recommendations.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-1">Recommendations:</h4>
                      <ul className="text-sm text-muted-foreground list-disc list-inside">
                        {result.recommendations.map((rec, i) => (
                          <li key={i}>{rec}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="mt-6 flex gap-4">
        <Button 
          onClick={() => setTestResults([])}
          variant="outline"
          disabled={!!currentTest}
        >
          Clear Results
        </Button>
        <Button 
          onClick={() => window.location.reload()}
          variant="outline"
          disabled={!!currentTest}
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh Page
        </Button>
      </div>
    </div>
  );
}
