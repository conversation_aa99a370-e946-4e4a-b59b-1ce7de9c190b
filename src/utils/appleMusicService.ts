import { MusicKit } from "@/types/musickit";
import { supabase } from '@/integrations/supabase/client';
import { AppleMusicTokenService } from '@/services/AppleMusicTokenService';

// We'll use the MusicKit type imported from the types file instead of redeclaring it
let musicKitInstance: MusicKit | null = null;
let isInitialized = false;
let isInitializing = false; // Add this flag to prevent concurrent initialization
// Script loading flags removed - MusicKit.js now loaded via index.html

/**
 * Fetch the Apple Music Developer Token from Supabase Edge Function
 */
const fetchDeveloperToken = async (): Promise<string | null> => {
  try {
    console.log('Fetching Apple Music Developer Token from Edge Function...');

    const { data, error } = await supabase.functions.invoke('apple-developer-token', {
      method: 'GET',
    });

    if (error) {
      console.error('Error fetching Apple Music Developer Token:', error);
      return null;
    }

    if (!data || !data.token) {
      console.error('No token returned from Edge Function');
      return null;
    }

    console.log('Successfully fetched Apple Music Developer Token from Edge Function');
    return data.token;
  } catch (error) {
    console.error('Failed to fetch Apple Music Developer Token:', error);
    return null;
  }
};

/**
 * Initialize MusicKit with the developer token
 */
export const initializeMusicKit = async (): Promise<boolean> => {
  // Prevent concurrent initialization
  if (isInitialized) return true;
  if (isInitializing) {
    console.log('MusicKit initialization already in progress, waiting...');
    // Wait for the initialization to complete
    while (isInitializing) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    return isInitialized;
  }

  try {
    isInitializing = true;

    // MusicKit.js is now loaded via index.html script tag
    // Wait for MusicKit to be available if not already loaded
    if (!window.MusicKit) {
      console.log('MusicKit is not loaded yet, waiting for it to be available...');
      await new Promise<void>((resolve) => {
        const checkMusicKit = () => {
          if (window.MusicKit) {
            resolve();
          } else {
            setTimeout(checkMusicKit, 100);
          }
        };
        checkMusicKit();
      });
    }

    // Try to get token from Edge Function directly
    console.log('Fetching Apple Music Developer Token...');
    const developerToken = await fetchDeveloperToken();

    if (!developerToken) {
      console.error('Failed to get Apple Music Developer Token - check Supabase Edge Function');
      isInitializing = false;
      return false;
    }

    // Configure MusicKit with the token
    console.log('Configuring MusicKit with Developer Token');
    await window.MusicKit.configure({
      developerToken: developerToken,
      app: {
        name: 'PlayBeg',
        build: '1.0',
      }
    });

    musicKitInstance = window.MusicKit.getInstance();
    isInitialized = true;
    console.log('MusicKit initialized successfully with token from Supabase');
    return true;
  } catch (error) {
    console.error('Failed to initialize MusicKit:', error);
    return false;
  } finally {
    isInitializing = false;
  }
};

/**
 * Initialize MusicKit with specific DJ tokens from session
 * This method allows for using a DJ's tokens instead of the user's own
 */
export const initializeMusicKitWithDJTokens = async (
  developerToken: string,
  userToken: string
): Promise<boolean> => {
  if (isInitializing) {
    console.log('MusicKit initialization already in progress, waiting...');
    // Wait for the initialization to complete
    while (isInitializing) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  try {
    isInitializing = true;

    // Wait for MusicKit to be loaded
    if (!window.MusicKit) {
      console.log('MusicKit is not loaded yet, waiting...');
      await new Promise<void>((resolve) => {
        const checkMusicKit = () => {
          if (window.MusicKit) {
            resolve();
          } else {
            setTimeout(checkMusicKit, 100);
          }
        };
        checkMusicKit();
      });
    }

    // Configure MusicKit with the DJ's tokens
    console.log('Configuring MusicKit with DJ\'s tokens');
    let instance: MusicKit;

    // If already initialized, get current instance
    if (isInitialized && musicKitInstance) {
      instance = musicKitInstance;
    } else {
      // Otherwise configure a new instance
      await window.MusicKit.configure({
        developerToken: developerToken,
        app: {
          name: 'PlayBeg',
          build: '1.0',
        }
      });
      instance = window.MusicKit.getInstance();
    }

    // Authorize with the user token
    instance.musicUserToken = userToken;
    isInitialized = true;
    musicKitInstance = instance;

    console.log('MusicKit initialized with DJ\'s token successfully');
    return true;
  } catch (error) {
    console.error('Failed to initialize MusicKit with DJ tokens:', error);
    return false;
  } finally {
    isInitializing = false;
  }
};

/**
 * Load the MusicKit script
 * COMMENTED OUT: MusicKit.js is now loaded via index.html script tag
 */
/*
const loadMusicKitScript = async (): Promise<void> => {
  // If script is already loaded or loading, just wait for it
  if (scriptLoaded) return;

  if (isScriptLoading) {
    console.log('MusicKit script is already loading, waiting...');
    while (isScriptLoading) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    return;
  }

  isScriptLoading = true;

  try {
    console.log('Loading MusicKit script...');

    // Check if script is already in document
    if (document.getElementById('apple-musickit-script')) {
      console.log('MusicKit script already in document');
      scriptLoaded = true;
      isScriptLoading = false;
      return;
    }

    // Create and load the script
    await new Promise<void>((resolve, reject) => {
      const script = document.createElement('script');
      script.id = 'apple-musickit-script';
      script.src = 'https://js-cdn.music.apple.com/musickit/v1/musickit.js';
      script.async = true;

      script.onload = () => {
        console.log('MusicKit script loaded successfully');
        scriptLoaded = true;
        resolve();
      };

      script.onerror = (error) => {
        console.error('Error loading MusicKit script:', error);
        reject(new Error('Failed to load MusicKit script'));
      };

      document.head.appendChild(script);
    });

    // Wait for MusicKit to be available in window
    if (!window.MusicKit) {
      console.log('Waiting for MusicKit to be available in window...');
      await new Promise<void>((resolve) => {
        const checkMusicKit = () => {
          if (window.MusicKit) {
            resolve();
          } else {
            setTimeout(checkMusicKit, 100);
          }
        };
        checkMusicKit();
      });
    }

    console.log('MusicKit is now available in window');
  } catch (error) {
    console.error('Failed to load MusicKit script:', error);
    throw error;
  } finally {
    isScriptLoading = false;
  }
};
*/

export const getMusicKit = async (): Promise<MusicKit | null> => {
  if (!isInitialized) {
    await initializeMusicKit();
  }
  return musicKitInstance;
};

/**
 * Authorize the user with Apple Music
 */
export const authorizeUser = async (): Promise<boolean> => {
  try {
    const music = await getMusicKit();
    if (!music) return false;

    // Request authorization from user
    if (!music.isAuthorized) {
      await music.authorize();
    }

    return music.isAuthorized;
  } catch (error) {
    console.error('Error authorizing with Apple Music:', error);
    return false;
  }
};

/**
 * Unauthorize the user from Apple Music
 */
export const unauthorizeUser = async (): Promise<boolean> => {
  try {
    const music = await getMusicKit();
    if (!music) return false;

    if (music.isAuthorized) {
      await music.unauthorize();
      console.log('Successfully unauthorized from Apple Music');
    }

    return !music.isAuthorized;
  } catch (error) {
    console.error('Error unauthorizing from Apple Music:', error);
    return false;
  }
};

/**
 * Search for songs in Apple Music
 *
 * This function has two implementations:
 * 1. Direct MusicKit API search (used by DJs who are authenticated)
 * 2. Proxy endpoint search (used by requesters who don't have authentication)
 */
export const searchSongs = async (query: string, limit: number = 10, sessionId?: string): Promise<any[]> => {
  // If no query, return empty results
  if (!query.trim()) return [];

  try {
    // If sessionId is provided, use the proxy endpoint (for requesters)
    if (sessionId) {
      console.log(`Using proxy endpoint for search with sessionId: ${sessionId}`);
      // Make sure sessionId is a string
      const sessionIdStr = String(sessionId);
      return await searchSongsViaProxy(query, limit, sessionIdStr);
    }

    // Otherwise use direct MusicKit API (for authenticated DJs)
    console.log('Using direct MusicKit API for search');
    const music = await getMusicKit();
    if (!music) {
      console.error('MusicKit not initialized');
      return [];
    }

    const results = await music.api.search(query, {
      types: 'songs',
      limit: limit
    });

    if (results && results.songs && results.songs.data) {
      return results.songs.data.map((song: any) => {
        // Debug genre extraction
        const primaryGenre = song.attributes.primaryGenreName;
        const genreNames = song.attributes.genreNames;
        const extractedGenre = primaryGenre || (genreNames && genreNames[0]) || undefined;

        console.log(`Direct API - Song: ${song.attributes.name} - Primary Genre: ${primaryGenre}, Genre Names: ${JSON.stringify(genreNames)}, Extracted: ${extractedGenre}`);

        return {
          id: song.id,
          title: song.attributes.name,
          artist: song.attributes.artistName,
          album: song.attributes.albumName,
          artwork: song.attributes.artwork ?
            song.attributes.artwork.url.replace('{w}', '100').replace('{h}', '100') : null,
          genre: extractedGenre
        };
      });
    }

    return [];
  } catch (error) {
    console.error('Error searching Apple Music:', error);
    return [];
  }
};

/**
 * Search for songs via our secure proxy endpoint
 * This keeps the Apple Music token on the server side
 */
const searchSongsViaProxy = async (query: string, limit: number = 10, sessionId: string): Promise<any[]> => {
  try {
    // Client-side caching for better performance
    const cacheKey = `search:${sessionId}:${query}:${limit}`;
    const cachedResults = sessionStorage.getItem(cacheKey);

    if (cachedResults) {
      console.log('Using cached search results from session storage');
      return JSON.parse(cachedResults);
    }

    // Get the current session token for authorization if available
    const { data: sessionData } = await supabase.auth.getSession();
    const accessToken = sessionData?.session?.access_token || '';

    // Prepare the request URL and headers
    const proxyUrl = `https://lbwqwecunykdhenvdrbz.functions.supabase.co/apple-music-search?sessionId=${encodeURIComponent(sessionId)}&query=${encodeURIComponent(query)}&limit=${limit}`;
    const headers: HeadersInit = {};

    // Add authorization header if we have a token, but don't require it
    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
    }

    console.log(`Fetching from proxy: ${proxyUrl}`);
    console.log(`Request has authorization: ${!!accessToken}`);

    const response = await fetch(proxyUrl, { headers });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Proxy search error (${response.status}):`, errorText);
      return [];
    }

    const results = await response.json();

    // Cache results in session storage (5 minute TTL)
    if (results && results.length > 0) {
      try {
        sessionStorage.setItem(cacheKey, JSON.stringify(results));
        // Set expiration
        const expiration = Date.now() + (5 * 60 * 1000); // 5 minutes
        sessionStorage.setItem(`${cacheKey}:expires`, expiration.toString());
        console.log('Cached search results in session storage');
      } catch (e) {
        console.warn('Failed to cache search results:', e);
      }
    }

    return results || [];
  } catch (error) {
    console.error('Error searching via proxy:', error);
    return [];
  }
};

/**
 * Clear expired cache entries from session storage
 */
export const clearExpiredCache = () => {
  const now = Date.now();

  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    if (key && key.endsWith(':expires')) {
      const expiration = parseInt(sessionStorage.getItem(key) || '0');
      if (now > expiration) {
        // Remove the expired item
        const cacheKey = key.replace(':expires', '');
        sessionStorage.removeItem(cacheKey);
        sessionStorage.removeItem(key);
        console.log(`Cleared expired cache entry: ${cacheKey}`);
      }
    }
  }
};

// Call this periodically to clean up expired cache entries
setInterval(clearExpiredCache, 60000); // Clean up every minute

/**
 * Delete a playlist from Apple Music
 */
export const deleteAppleMusicPlaylist = async (playlistId: string, djUserId?: string): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    console.log(`Attempting to delete Apple Music playlist: ${playlistId}`);

    let music;
    let musicUserToken;

    if (djUserId) {
      // Use DJ's token for playlist deletion
      console.log('Using DJ token for playlist deletion');

      const { data: tokenData, error: tokenError } = await supabase
        .from('apple_music_tokens')
        .select('apple_music_token')
        .eq('user_id', djUserId)
        .eq('is_valid', true)
        .order('created_at', { ascending: false })
        .maybeSingle();

      if (tokenError || !tokenData?.apple_music_token) {
        console.warn('No valid Apple Music token found for DJ:', tokenError?.message || 'No token data');
        // Don't fail the deletion if we can't get the token - just skip playlist deletion
        return { success: true, error: 'No Apple Music token available - playlist deletion skipped' };
      }

      musicUserToken = tokenData.apple_music_token;
      music = await getMusicKit();

      if (!music) {
        return { success: false, error: 'MusicKit not initialized' };
      }
    } else {
      // Use current user's MusicKit authorization
      music = await getMusicKit();
      if (!music || !music.isAuthorized) {
        return { success: false, error: 'MusicKit not initialized or user not authorized' };
      }
      musicUserToken = music.musicUserToken;
    }

    if (!musicUserToken) {
      return { success: false, error: 'No Apple Music user token available' };
    }

    // Delete playlist using Apple Music API
    const response = await fetch(`https://api.music.apple.com/v1/me/library/playlists/${playlistId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${music.developerToken}`,
        'Music-User-Token': musicUserToken,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Failed to delete Apple Music playlist. Status:', response.status, 'Response:', errorText);
      return {
        success: false,
        error: `Failed to delete playlist: ${response.status} ${response.statusText}`
      };
    }

    console.log('Successfully deleted Apple Music playlist');
    return { success: true };

  } catch (error) {
    console.error('Error deleting Apple Music playlist:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Create a new playlist in Apple Music
 */
export const createPlaylist = async (name: string, description: string = ''): Promise<any> => {
  try {
    const music = await getMusicKit();
    if (!music) {
      throw new Error('MusicKit not initialized');
    }

    if (!music.isAuthorized) {
      console.log('MusicKit not authorized, attempting to authorize...');
      await music.authorize();

      if (!music.isAuthorized) {
        throw new Error('User declined Apple Music authorization');
      }
    }

    // Ensure we have a valid musicUserToken
    if (!music.musicUserToken) {
      throw new Error('Missing Apple Music user token');
    }

    console.log('Creating Apple Music playlist with valid user token');

    // Properly format the request body according to Apple Music API requirements
    const requestBody = {
      attributes: {
        name: name,
        description: description || ""
      }
    };

    // Log the request body for debugging
    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    // Create playlist using the direct REST API call with proper JSON structure
    const response = await fetch('https://api.music.apple.com/v1/me/library/playlists', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${music.developerToken}`,
        'Music-User-Token': music.musicUserToken,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Failed to create playlist. Status:', response.status, 'Response:', errorText);

      let errorMessage;
      try {
        const errorData = JSON.parse(errorText);
        errorMessage = errorData?.errors?.[0]?.title || response.statusText;
      } catch (e) {
        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      }

      throw new Error(`Failed to create playlist: ${errorMessage}`);
    }

    // Only try to parse JSON if we got a successful response
    const data = await response.json();
    console.log('Successfully created Apple Music playlist:', data);
    return data?.data?.[0];
  } catch (error) {
    console.error('Error creating playlist:', error);
    throw error;
  }
};

/**
 * Add a song to a playlist
 * @param playlistId The Apple Music ID of the playlist (e.g., "p.XXXXXXXX")
 * @param songId The Apple Music ID of the song (e.g., "1234567890")
 * @param requestId Optional request ID to update in the database
 * @returns Object with success status and error details if applicable
 */
/**
 * Legacy function that now uses the direct method
 * @deprecated Use addSongToPlaylistDirect instead
 */
export const addSongToPlaylist = async (playlistId: string, songId: string, requestId?: string): Promise<{
  success: boolean;
  status?: number;
  error?: string;
  errorText?: string;
}> => {
  console.log(`Legacy addSongToPlaylist called, using direct method instead`);

  try {
    // Get the current user ID to pass to the direct method
    let userId: string | undefined;
    try {
      userId = await getUserId();
    } catch (error) {
      console.warn('Could not get user ID, continuing without it:', error);
    }

    // Call the direct method
    return await addSongToPlaylistDirect(playlistId, songId, requestId, userId);
  } catch (error) {
    console.error('Error in legacy addSongToPlaylist:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Check if user is authorized with Apple Music
 */
export const isAuthorized = async (): Promise<boolean> => {
  try {
    const music = await getMusicKit();
    return music && music.isAuthorized;
  } catch (error) {
    console.error('Error checking authorization:', error);
    return false;
  }
};

/**
 * Add a song to a playlist using direct Apple Music API (no Edge Function)
 * @param playlistId The Apple Music ID of the playlist (e.g., "p.XXXXXXXX")
 * @param songId The Apple Music ID of the song (e.g., "1234567890")
 * @param requestId Optional request ID to update in the database
 * @param djUserId Optional DJ user ID to get the correct token
 * @returns Object with success status and error details if applicable
 */
export const addSongToPlaylistDirect = async (
  playlistId: string,
  songId: string,
  requestId?: string,
  djUserId?: string
): Promise<{
  success: boolean;
  status?: number;
  error?: string;
  errorText?: string;
}> => {
  console.log(`Adding song ${songId} to playlist ${playlistId} using direct Apple Music API`);
  console.log(`Request ID: ${requestId || 'none'}, DJ User ID: ${djUserId || 'none'}`);

  try {
    // Step 1: Initialize MusicKit
    const music = await getMusicKit();
    if (!music) {
      throw new Error('MusicKit not initialized');
    }

    // Step 2: Get the correct token
    let musicUserToken = music.musicUserToken;

    // If we have a DJ user ID, try to get their token using our centralized service
    if (djUserId) {
      try {
        console.log(`Getting Apple Music token for DJ ${djUserId} using centralized service`);

        // Use the centralized token service to get the token
        const token = await AppleMusicTokenService.getTokenForCurrentUser();

        if (token) {
          console.log('Successfully retrieved DJ\'s Apple Music token');
          musicUserToken = token;
        } else {
          console.warn('Failed to get DJ\'s token from centralized service, falling back to developer token');
          // Get the developer token as a fallback
          const developerToken = await AppleMusicTokenService.getDeveloperToken();
          if (developerToken) {
            console.log('Using developer token as fallback');
            musicUserToken = developerToken;
          } else {
            console.error('Failed to get developer token as fallback');
            throw new Error('Could not get a valid Apple Music token for the DJ');
          }
        }
      } catch (tokenError) {
        console.error('Error getting DJ token:', tokenError);

        // Try to get the developer token as a fallback
        try {
          console.log('Attempting to use developer token as fallback');
          const developerToken = await AppleMusicTokenService.getDeveloperToken();
          if (developerToken) {
            console.log('Using developer token as fallback after error');
            musicUserToken = developerToken;
          } else {
            throw new Error('Failed to get developer token as fallback');
          }
        } catch (devTokenError) {
          console.error('Error getting developer token:', devTokenError);
          throw new Error('Failed to retrieve any valid Apple Music token');
        }
      }
    } else if (!musicUserToken) {
      // If no DJ user ID and no current token, use the developer token instead of trying to authorize
      // This avoids prompting the requester to log in to Apple Music
      console.log('No token available, using developer token instead of trying to authorize');
      try {
        const developerToken = await AppleMusicTokenService.getDeveloperToken();
        if (developerToken) {
          console.log('Using developer token as fallback');
          musicUserToken = developerToken;
        } else {
          throw new Error('Failed to get developer token as fallback');
        }
      } catch (devTokenError) {
        console.error('Error getting developer token:', devTokenError);
        throw new Error('Failed to retrieve any valid Apple Music token');
      }
    }

    // Step 3: Ensure we have a valid developer token
    if (!music.developerToken) {
      console.log('Missing developer token, fetching from server...');
      const developerToken = await fetchDeveloperToken();
      if (!developerToken) {
        throw new Error('Failed to get Apple Music Developer Token');
      }
    }

    // Step 4: Check if the playlistId is a UUID (database ID) or an Apple Music ID
    if (!/^p\./i.test(playlistId)) {
      // If it's not an Apple Music ID (doesn't start with 'p.'), try to get the Apple Music ID from the database
      console.log('Playlist ID does not appear to be an Apple Music ID, fetching from database...');

      try {
        // Use a direct query with proper headers
        const { data: playlistData, error: playlistError } = await supabase
          .from('playlists')
          .select('apple_music_playlist_id')
          .eq('id', playlistId)
          .single();

        if (playlistError) {
          console.error('Error fetching playlist details:', playlistError);
        } else if (playlistData && playlistData.apple_music_playlist_id) {
          console.log(`Found Apple Music playlist ID: ${playlistData.apple_music_playlist_id}`);
          playlistId = playlistData.apple_music_playlist_id;
        }
      } catch (dbError) {
        console.error('Error fetching playlist from database:', dbError);
      }
    }

    // If we still don't have a valid Apple Music playlist ID (starting with 'p.'),
    // and we have a DJ user ID, try to create a default playlist
    if (!/^p\./i.test(playlistId) && djUserId) {
      console.log('No valid Apple Music playlist ID found, attempting to create a default playlist');
      const result = await createDefaultPlaylistForDJ(djUserId);

      if (result && result.appleMusicPlaylistId) {
        console.log(`Created default playlist with ID: ${result.appleMusicPlaylistId}`);
        playlistId = result.appleMusicPlaylistId;
      } else {
        console.error('Failed to create default playlist');
        throw new Error('No valid Apple Music playlist ID found and could not create a default playlist');
      }
    }

    // Step 5: Make the API call to add the song to the playlist
    console.log(`Making API call to add song ${songId} to playlist ${playlistId}`);
    console.log(`Using token length: ${musicUserToken.length}`);

    const response = await fetch(`https://api.music.apple.com/v1/me/library/playlists/${playlistId}/tracks`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${music.developerToken}`,
        'Music-User-Token': musicUserToken,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        data: [
          {
            id: songId,
            type: 'songs'
          }
        ]
      })
    });

    // Step 6: Handle the response
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Failed to add song to playlist. Status:', response.status, 'Response:', errorText);

      return {
        success: false,
        status: response.status,
        error: response.statusText,
        errorText: errorText
      };
    }

    console.log('Successfully added song to playlist via direct API call');

    // Step 7: Update the database if we have a request ID
    if (requestId) {
      try {
        console.log(`Updating request ${requestId} to mark as added to playlist`);
        const { error: updateError } = await supabase
          .from('song_requests')
          .update({ added_to_playlist: true })
          .eq('id', requestId);

        if (updateError) {
          console.error('Error updating song request:', updateError);
        } else {
          console.log(`Successfully marked request ${requestId} as added to playlist`);

          // Also update any other requests for the same song in this session
          try {
            // First get the session ID for this request
            const { data: requestData, error: requestError } = await supabase
              .from('song_requests')
              .select('session_id, apple_music_id')
              .eq('id', requestId)
              .single();

            if (!requestError && requestData) {
              console.log(`Updating all related requests for song ${requestData.apple_music_id}`);

              // Update all requests for the same song in this session
              const { error: bulkUpdateError } = await supabase
                .from('song_requests')
                .update({ added_to_playlist: true })
                .eq('session_id', requestData.session_id)
                .eq('apple_music_id', requestData.apple_music_id);

              if (bulkUpdateError) {
                console.error('Error updating related requests:', bulkUpdateError);
              } else {
                console.log('Successfully updated all related requests');
              }
            }
          } catch (relatedError) {
            console.error('Error updating related requests:', relatedError);
          }
        }
      } catch (dbError) {
        console.error('Error updating database:', dbError);
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error adding song to playlist:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Get the current user ID from Supabase auth
 */
export const getUserId = async (): Promise<string> => {
  try {
    const { data } = await supabase.auth.getUser();
    return data.user?.id || '';
  } catch (error) {
    console.error('Error getting user ID:', error);
    return '';
  }
};

/**
 * Create a default playlist for a DJ if none exists
 * This ensures song requests can be properly processed
 * @param djUserId The DJ's user ID
 * @param sessionId Optional session ID to associate with the playlist
 * @returns Object containing the created playlist's Apple Music ID and database ID, or null if creation failed
 */
export const createDefaultPlaylistForDJ = async (djUserId: string, sessionId?: string): Promise<{ appleMusicPlaylistId: string; playlistId: string } | null> => {
  try {
    console.log(`Creating default playlist for DJ ${djUserId}`);

    // First, check if the DJ already has any playlists
    const { data: existingPlaylists, error: playlistError } = await supabase
      .from('playlists')
      .select('id, apple_music_playlist_id, active')
      .eq('user_id', djUserId)
      .order('created_at', { ascending: false });

    if (playlistError) {
      console.error('Error checking for existing playlists:', playlistError);
      return null;
    }

    // If the DJ already has playlists, use the most recent one
    if (existingPlaylists && existingPlaylists.length > 0) {
      console.log(`DJ already has ${existingPlaylists.length} playlists`);

      // Check if any playlist is active
      const activePlaylist = existingPlaylists.find(p => p.active);
      if (activePlaylist && activePlaylist.apple_music_playlist_id) {
        console.log(`Using existing active playlist: ${activePlaylist.apple_music_playlist_id}`);
        return {
          appleMusicPlaylistId: activePlaylist.apple_music_playlist_id,
          playlistId: activePlaylist.id
        };
      }

      // If no active playlist, use the most recent one and set it as active
      const mostRecentPlaylist = existingPlaylists[0];
      if (mostRecentPlaylist && mostRecentPlaylist.apple_music_playlist_id) {
        console.log(`Setting most recent playlist as active: ${mostRecentPlaylist.apple_music_playlist_id}`);

        // Update the playlist to be active
        const { error: updateError } = await supabase
          .from('playlists')
          .update({ active: true })
          .eq('id', mostRecentPlaylist.id);

        if (updateError) {
          console.error('Error setting playlist as active:', updateError);
        }

        return {
          appleMusicPlaylistId: mostRecentPlaylist.apple_music_playlist_id,
          playlistId: mostRecentPlaylist.id
        };
      }
    }

    // If we get here, the DJ has no playlists or no playlists with Apple Music IDs
    // We need to create a new one using the DJ's token

    // Get the DJ's Apple Music token using our centralized service
    let djToken;
    try {
      console.log('Getting DJ\'s Apple Music token using centralized service');
      djToken = await AppleMusicTokenService.getTokenForCurrentUser();

      if (!djToken) {
        console.error('Failed to get DJ\'s token from centralized service');
        return null;
      }
    } catch (tokenError) {
      console.error('Error getting DJ\'s Apple Music token:', tokenError);
      return null;
    }

    // Initialize MusicKit with the DJ's token
    const music = await getMusicKit();
    if (!music) {
      console.error('MusicKit not initialized');
      return null;
    }

    // Set the DJ's token - this is critical for ensuring the playlist is created in the DJ's Apple Music account
    // Without this, the playlist would be created in the current user's account, which would not work for song requests
    music.musicUserToken = djToken;

    // Create a new playlist with a timestamp to ensure uniqueness
    const now = new Date();
    const formattedDate = now.toLocaleDateString();
    const formattedTime = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    const playlistName = `PlayBeg Default - ${formattedDate} ${formattedTime}`;
    const playlistDescription = 'Automatically created playlist for song requests';

    // Create the playlist in Apple Music
    const requestBody = {
      attributes: {
        name: playlistName,
        description: playlistDescription
      }
    };

    const response = await fetch('https://api.music.apple.com/v1/me/library/playlists', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${music.developerToken}`,
        'Music-User-Token': djToken,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Failed to create default playlist. Status:', response.status, 'Response:', errorText);
      console.error('This might be due to Apple Music API rate limits or authentication issues');
      // Log additional information to help with debugging
      console.error('Request details:', {
        url: 'https://api.music.apple.com/v1/me/library/playlists',
        method: 'POST',
        tokenLength: djToken.length,
        developerTokenLength: music.developerToken.length
      });
      return null;
    }

    const data = await response.json();
    console.log('Successfully created default Apple Music playlist:', data);

    if (!data?.data?.[0]?.id) {
      console.error('No playlist ID returned from Apple Music');
      return null;
    }

    const appleMusicPlaylistId = data.data[0].id;

    // Save the playlist to the database
    const { data: newPlaylist, error: insertError } = await supabase
      .from('playlists')
      .insert({
        user_id: djUserId,
        name: playlistName,
        description: playlistDescription,
        apple_music_playlist_id: appleMusicPlaylistId,
        active: true
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error saving playlist to database:', insertError);
      // Even if the database insert fails, return the Apple Music playlist ID
      // so we can still use it for this request
      return {
        appleMusicPlaylistId: appleMusicPlaylistId,
        playlistId: 'temp-' + appleMusicPlaylistId // Use a temporary ID since we couldn't save to DB
      };
    }

    console.log('Successfully created and saved default playlist:', newPlaylist);
    return {
      appleMusicPlaylistId: appleMusicPlaylistId,
      playlistId: newPlaylist.id
    };
  } catch (error) {
    console.error('Error creating default playlist for DJ:', error);
    return null;
  }
};
