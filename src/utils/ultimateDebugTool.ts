/**
 * 🔧 ULTIMATE PLAYBEG DEBUG TOOL - ROOT CAUSE DETECTOR
 * 
 * This tool automatically identifies the root cause of "Loading Sessions..." issues
 * by monitoring all critical systems simultaneously and providing automated analysis.
 */

interface DebugEvent {
  timestamp: number;
  category: 'AUTH' | 'COMPONENT' | 'WEBSOCKET' | 'DATA' | 'ROUTER' | 'ERROR';
  type: string;
  data: any;
  severity: 'INFO' | 'WARN' | 'ERROR' | 'CRITICAL';
}

interface ComponentLifecycle {
  name: string;
  mountTime: number;
  unmountTime?: number;
  renderCount: number;
  lastRender: number;
}

interface AuthStateTransition {
  from: any;
  to: any;
  timestamp: number;
  event: string;
  causedRemount: boolean;
}

interface WebSocketAttempt {
  timestamp: number;
  url: string;
  status: 'CONNECTING' | 'CONNECTED' | 'FAILED' | 'CLOSED';
  error?: string;
  duration?: number;
}

class UltimateDebugTool {
  private events: DebugEvent[] = [];
  private components: Map<string, ComponentLifecycle> = new Map();
  private authTransitions: AuthStateTransition[] = [];
  private websocketAttempts: WebSocketAttempt[] = [];
  private startTime: number = performance.now();
  private isRecording: boolean = false;
  private analysisResults: any = null;

  constructor() {
    this.setupGlobalErrorHandling();
    this.setupWebSocketMonitoring();
    this.setupPerformanceMonitoring();
  }

  // 🎯 START COMPREHENSIVE MONITORING
  startMonitoring(testName: string = 'Dashboard Refresh Test') {
    console.log(`🔧 ULTIMATE DEBUG: Starting monitoring for "${testName}"`);
    this.isRecording = true;
    this.startTime = performance.now();
    this.events = [];
    this.components.clear();
    this.authTransitions = [];
    this.websocketAttempts = [];
    
    this.logEvent('INFO', 'SYSTEM', 'MONITORING_STARTED', { testName });
    
    // Setup automatic analysis after 10 seconds
    setTimeout(() => {
      if (this.isRecording) {
        this.stopMonitoringAndAnalyze();
      }
    }, 10000);
  }

  // 🛑 STOP MONITORING AND ANALYZE
  stopMonitoringAndAnalyze() {
    if (!this.isRecording) return;
    
    this.isRecording = false;
    this.logEvent('INFO', 'SYSTEM', 'MONITORING_STOPPED', {});
    
    console.log(`🔧 ULTIMATE DEBUG: Stopping monitoring and analyzing...`);
    this.analysisResults = this.performRootCauseAnalysis();
    this.generateReport();
    
    return this.analysisResults;
  }

  // 📊 LOG EVENT
  logEvent(severity: DebugEvent['severity'], category: DebugEvent['category'], type: string, data: any) {
    if (!this.isRecording) return;
    
    const event: DebugEvent = {
      timestamp: performance.now() - this.startTime,
      category,
      type,
      data,
      severity
    };
    
    this.events.push(event);
    
    // Real-time critical event detection
    if (severity === 'CRITICAL' || severity === 'ERROR') {
      console.error(`🚨 CRITICAL EVENT: ${category}:${type}`, data);
    }
  }

  // 🧩 COMPONENT LIFECYCLE TRACKING
  trackComponentMount(componentName: string) {
    const existing = this.components.get(componentName);
    const now = performance.now();
    
    if (existing && !existing.unmountTime) {
      // Component remounting without unmounting - CRITICAL ISSUE
      this.logEvent('CRITICAL', 'COMPONENT', 'REMOUNT_WITHOUT_UNMOUNT', {
        component: componentName,
        previousMountTime: existing.mountTime,
        newMountTime: now,
        timeSinceLastMount: now - existing.mountTime
      });
    }
    
    this.components.set(componentName, {
      name: componentName,
      mountTime: now,
      unmountTime: undefined,
      renderCount: 1,
      lastRender: now
    });
    
    this.logEvent('INFO', 'COMPONENT', 'MOUNT', { component: componentName, timestamp: now });
  }

  trackComponentUnmount(componentName: string) {
    const component = this.components.get(componentName);
    if (component) {
      const now = performance.now();
      component.unmountTime = now;
      const lifespan = now - component.mountTime;
      
      // Short lifespan indicates instability
      if (lifespan < 1000) {
        this.logEvent('WARN', 'COMPONENT', 'SHORT_LIFESPAN', {
          component: componentName,
          lifespan,
          renderCount: component.renderCount
        });
      }
      
      this.logEvent('INFO', 'COMPONENT', 'UNMOUNT', { 
        component: componentName, 
        lifespan,
        renderCount: component.renderCount 
      });
    }
  }

  trackComponentRender(componentName: string, props?: any) {
    const component = this.components.get(componentName);
    if (component) {
      const now = performance.now();
      component.renderCount++;
      const timeSinceLastRender = now - component.lastRender;
      component.lastRender = now;
      
      // Rapid re-renders indicate instability
      if (timeSinceLastRender < 100 && component.renderCount > 5) {
        this.logEvent('WARN', 'COMPONENT', 'RAPID_RERENDERS', {
          component: componentName,
          renderCount: component.renderCount,
          timeSinceLastRender,
          props
        });
      }
    }
  }

  // 🔐 AUTH STATE TRACKING
  trackAuthStateChange(event: string, fromState: any, toState: any) {
    const now = performance.now();
    
    // Check if this auth change could cause component remounts
    const userIdChanged = fromState?.user?.id !== toState?.user?.id;
    const loadingChanged = fromState?.loading !== toState?.loading;
    const causedRemount = userIdChanged || (loadingChanged && this.hasRecentComponentActivity());
    
    const transition: AuthStateTransition = {
      from: fromState,
      to: toState,
      timestamp: now,
      event,
      causedRemount
    };
    
    this.authTransitions.push(transition);
    
    if (causedRemount) {
      this.logEvent('WARN', 'AUTH', 'STATE_CHANGE_CAUSED_REMOUNT', {
        event,
        userIdChanged,
        loadingChanged,
        fromUserId: fromState?.user?.id,
        toUserId: toState?.user?.id
      });
    }
    
    this.logEvent('INFO', 'AUTH', 'STATE_CHANGE', { event, fromState, toState, causedRemount });
  }

  // 🌐 WEBSOCKET MONITORING
  private setupWebSocketMonitoring() {
    // Override WebSocket constructor to monitor all connections
    const originalWebSocket = window.WebSocket;
    const self = this;
    
    window.WebSocket = function(url: string, protocols?: string | string[]) {
      const ws = new originalWebSocket(url, protocols);
      const startTime = performance.now();
      
      self.logEvent('INFO', 'WEBSOCKET', 'CONNECTION_ATTEMPT', { url, startTime });
      
      ws.addEventListener('open', () => {
        const duration = performance.now() - startTime;
        self.logEvent('INFO', 'WEBSOCKET', 'CONNECTION_SUCCESS', { url, duration });
      });
      
      ws.addEventListener('error', (error) => {
        self.logEvent('ERROR', 'WEBSOCKET', 'CONNECTION_ERROR', { url, error: error.toString() });
      });
      
      ws.addEventListener('close', (event) => {
        const duration = performance.now() - startTime;
        if (duration < 1000) {
          self.logEvent('CRITICAL', 'WEBSOCKET', 'PREMATURE_CLOSE', { 
            url, 
            duration, 
            code: event.code, 
            reason: event.reason 
          });
        } else {
          self.logEvent('INFO', 'WEBSOCKET', 'CONNECTION_CLOSED', { url, duration, code: event.code });
        }
      });
      
      return ws;
    } as any;
  }

  // 🚨 GLOBAL ERROR HANDLING
  private setupGlobalErrorHandling() {
    window.addEventListener('error', (event) => {
      this.logEvent('ERROR', 'ERROR', 'JAVASCRIPT_ERROR', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      });
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.logEvent('ERROR', 'ERROR', 'UNHANDLED_PROMISE_REJECTION', {
        reason: event.reason,
        stack: event.reason?.stack
      });
    });
  }

  // ⚡ PERFORMANCE MONITORING
  private setupPerformanceMonitoring() {
    // Monitor long tasks that could block the main thread
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) {
            this.logEvent('WARN', 'DATA', 'LONG_TASK', {
              duration: entry.duration,
              startTime: entry.startTime
            });
          }
        }
      });
      observer.observe({ entryTypes: ['longtask'] });
    }
  }

  // 🔍 ROOT CAUSE ANALYSIS
  private performRootCauseAnalysis() {
    const analysis = {
      rootCause: 'UNKNOWN',
      confidence: 0,
      evidence: [] as string[],
      recommendations: [] as string[],
      timeline: this.buildTimeline(),
      componentStability: this.analyzeComponentStability(),
      authStability: this.analyzeAuthStability(),
      websocketHealth: this.analyzeWebSocketHealth(),
      criticalEvents: this.events.filter(e => e.severity === 'CRITICAL' || e.severity === 'ERROR')
    };

    // Analyze patterns to determine root cause
    if (this.hasAuthRelatedRemounts()) {
      analysis.rootCause = 'AUTH_STATE_INSTABILITY';
      analysis.confidence = 0.9;
      analysis.evidence.push('Auth state changes causing component remounts');
      analysis.recommendations.push('Implement auth state stabilization');
    } else if (this.hasWebSocketFailures()) {
      analysis.rootCause = 'WEBSOCKET_CONNECTION_FAILURES';
      analysis.confidence = 0.8;
      analysis.evidence.push('WebSocket connections failing rapidly');
      analysis.recommendations.push('Fix component lifecycle to allow WebSocket connections');
    } else if (this.hasComponentInstability()) {
      analysis.rootCause = 'COMPONENT_LIFECYCLE_INSTABILITY';
      analysis.confidence = 0.7;
      analysis.evidence.push('Components unmounting/remounting rapidly');
      analysis.recommendations.push('Investigate router or parent component issues');
    }

    return analysis;
  }

  // Helper methods for analysis
  private hasRecentComponentActivity(): boolean {
    const recentEvents = this.events.filter(e => 
      e.category === 'COMPONENT' && 
      (performance.now() - this.startTime - e.timestamp) < 1000
    );
    return recentEvents.length > 0;
  }

  private hasAuthRelatedRemounts(): boolean {
    return this.authTransitions.some(t => t.causedRemount);
  }

  private hasWebSocketFailures(): boolean {
    return this.events.some(e => 
      e.category === 'WEBSOCKET' && 
      (e.type === 'CONNECTION_ERROR' || e.type === 'PREMATURE_CLOSE')
    );
  }

  private hasComponentInstability(): boolean {
    return Array.from(this.components.values()).some(c => 
      c.renderCount > 10 || (c.unmountTime && (c.unmountTime - c.mountTime) < 1000)
    );
  }

  private buildTimeline() {
    return this.events
      .sort((a, b) => a.timestamp - b.timestamp)
      .map(e => `${e.timestamp.toFixed(0)}ms: ${e.category}:${e.type}`)
      .slice(0, 50); // First 50 events
  }

  private analyzeComponentStability() {
    const components = Array.from(this.components.values());
    return {
      totalComponents: components.length,
      unstableComponents: components.filter(c => c.renderCount > 10 || (c.unmountTime && (c.unmountTime - c.mountTime) < 1000)),
      averageLifespan: components.reduce((sum, c) => sum + (c.unmountTime ? c.unmountTime - c.mountTime : performance.now() - c.mountTime), 0) / components.length
    };
  }

  private analyzeAuthStability() {
    return {
      totalTransitions: this.authTransitions.length,
      remountCausingTransitions: this.authTransitions.filter(t => t.causedRemount).length,
      rapidTransitions: this.authTransitions.filter((t, i, arr) => 
        i > 0 && t.timestamp - arr[i-1].timestamp < 1000
      ).length
    };
  }

  private analyzeWebSocketHealth() {
    const wsEvents = this.events.filter(e => e.category === 'WEBSOCKET');
    return {
      totalAttempts: wsEvents.filter(e => e.type === 'CONNECTION_ATTEMPT').length,
      successfulConnections: wsEvents.filter(e => e.type === 'CONNECTION_SUCCESS').length,
      failures: wsEvents.filter(e => e.type === 'CONNECTION_ERROR').length,
      prematureCloses: wsEvents.filter(e => e.type === 'PREMATURE_CLOSE').length
    };
  }

  // 📋 GENERATE COMPREHENSIVE REPORT
  private generateReport() {
    if (!this.analysisResults) return;

    console.group('🔧 ULTIMATE DEBUG TOOL - ROOT CAUSE ANALYSIS REPORT');
    console.log('🎯 ROOT CAUSE:', this.analysisResults.rootCause);
    console.log('📊 CONFIDENCE:', (this.analysisResults.confidence * 100).toFixed(0) + '%');
    console.log('🔍 EVIDENCE:', this.analysisResults.evidence);
    console.log('💡 RECOMMENDATIONS:', this.analysisResults.recommendations);
    console.log('📈 COMPONENT STABILITY:', this.analysisResults.componentStability);
    console.log('🔐 AUTH STABILITY:', this.analysisResults.authStability);
    console.log('🌐 WEBSOCKET HEALTH:', this.analysisResults.websocketHealth);
    console.log('🚨 CRITICAL EVENTS:', this.analysisResults.criticalEvents.length);
    console.log('⏱️ TIMELINE (first 20 events):', this.analysisResults.timeline.slice(0, 20));
    console.groupEnd();

    // Also create a downloadable report
    this.createDownloadableReport();
  }

  private createDownloadableReport() {
    const report = {
      timestamp: new Date().toISOString(),
      analysis: this.analysisResults,
      allEvents: this.events,
      components: Array.from(this.components.entries()),
      authTransitions: this.authTransitions
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `playbeg-debug-report-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log('📁 Debug report downloaded as JSON file');
  }

  // 🎮 PUBLIC API FOR COMPONENTS
  getAnalysisResults() {
    return this.analysisResults;
  }

  isCurrentlyRecording() {
    return this.isRecording;
  }
}

// Create global instance
export const ultimateDebugTool = new UltimateDebugTool();

// Global access for manual testing
(window as any).ultimateDebugTool = ultimateDebugTool;

export default ultimateDebugTool;
