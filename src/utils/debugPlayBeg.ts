// src/utils/debugPlayBeg.ts
const LOG_PREFIX = "[PlayBeg DIAGNOSTIC]";
let sessionStartTime = performance.now();

// Call this at the very beginning of your app's lifecycle (e.g., index.tsx or App.tsx)
// and potentially at the start of key re-initialization sequences after refresh.
export function resetDebugTimer(eventName: string = "Timer Reset"): void {
    sessionStartTime = performance.now();
    console.log(`${LOG_PREFIX} ${eventName} at ${new Date().toISOString()}`);
}

function getElapsedTime(): string {
    return (performance.now() - sessionStartTime).toFixed(2);
}

export function diagLog(...args: any[]): void {
    console.log(`${LOG_PREFIX} [${getElapsedTime()}ms]`, ...args);
}

export function diagWarn(...args: any[]): void {
    console.warn(`${LOG_PREFIX} [${getElapsedTime()}ms]`, ...args);
}

export function diagError(...args: any[]): void {
    console.error(`${LOG_PREFIX} [${getElapsedTime()}ms]`, ...args);
}

export function diagInfo(key: string, value: any): void {
    // Using console.info for structured info, but could be console.log
    console.info(`${LOG_PREFIX} [${getElapsedTime()}ms] INFO: ${key}:`, value !== undefined ? value : 'undefined');
}

// Initialize timer when this module is loaded to capture the earliest possible point
resetDebugTimer("Logger Initialized (Module Load)");
